# 项目概述

这是一个包含API和Web前端的项目，用于管理和同步数据。

## 功能描述

### API功能
- 数据同步：支持实时数据同步功能
- 文件上传：支持文件上传与管理
- 聊天功能：提供实时聊天API

### Web前端功能
- 数据可视化：提供数据图表展示
- 文件管理：支持文件上传与下载
- 实时聊天：提供实时聊天界面

## 环境配置

### 系统要求
- Python 3.8+
- Node.js 16+

### 数据库
- PostgreSQL 12+

## 常见问题解答

Q: 如何配置数据库连接？
A: 修改api/config.py中的数据库连接配置

Q: 如何调试API？
A: 使用api/examples/debug_api_example.py进行调试

## 安装指南

1. 克隆项目仓库
2. 安装依赖：`pip install -r api/requirements.txt` 和 `npm install --prefix web`

## 使用说明

- 启动API服务器：`python api/main.py`
- 启动Web前端：`npm run dev --prefix web`

## 贡献指南

欢迎提交Pull Request。请确保代码风格一致并通过所有测试。
 