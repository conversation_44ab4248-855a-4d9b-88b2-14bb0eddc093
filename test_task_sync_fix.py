#!/usr/bin/env python3
"""
测试任务同步修复的脚本
验证任务监控器是否能自动发现RQ任务
"""

import requests
import time
import json

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试活跃任务API
        print("📡 测试 /tasks/active")
        response = requests.get(f"{base_url}/tasks/active", timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"  返回任务数: {len(data)}")
            
            for i, task in enumerate(data[:3]):
                print(f"  任务 {i+1}:")
                print(f"    ID: {task.get('task_id', 'Unknown')}")
                print(f"    状态: {task.get('status', 'Unknown')}")
                print(f"    名称: {task.get('task_name', 'Unknown')}")
                print(f"    自动同步: {task.get('auto_synced', False)}")
                print()
        else:
            print(f"  错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
        return False
    
    try:
        # 测试已完成任务API
        print("📡 测试 /tasks/completed")
        response = requests.get(f"{base_url}/tasks/completed?limit=5", timeout=10)
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"  返回任务数: {len(data)}")
        else:
            print(f"  错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
        return False
    
    return True

def create_test_task():
    """创建测试任务"""
    print("\n🚀 创建测试任务...")
    
    try:
        response = requests.post(
            "http://localhost:8000/tasks/run",
            json={
                "task_type": "sync_materials"
            },
            timeout=10
        )
        
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            task_id = data.get('task_id')
            print(f"  ✅ 任务创建成功: {task_id}")
            return task_id
        else:
            print(f"  ❌ 创建失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
        return None

def wait_and_check_task(task_id, max_wait=30):
    """等待并检查任务状态"""
    print(f"\n⏳ 等待任务 {task_id} 状态变化...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"http://localhost:8000/tasks/status/{task_id}", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'Unknown')
                print(f"  当前状态: {status}")
                
                if status in ['SUCCESS', 'FAILURE', 'REVOKED']:
                    print(f"  ✅ 任务已完成: {status}")
                    return True
                    
            time.sleep(2)
            
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 检查状态失败: {e}")
            time.sleep(2)
    
    print(f"  ⏰ 等待超时")
    return False

def test_task_sync():
    """测试任务同步功能"""
    print("🧪 测试任务同步功能...\n")
    
    # 1. 测试API端点
    if not test_api_endpoints():
        print("❌ API端点测试失败，请检查后端服务")
        return False
    
    print("✅ API端点测试通过\n")
    
    # 2. 创建测试任务
    task_id = create_test_task()
    if not task_id:
        print("❌ 无法创建测试任务")
        return False
    
    # 3. 等待一段时间让任务状态变化
    time.sleep(3)
    
    # 4. 再次检查API，看是否能看到任务
    print("\n🔄 重新检查任务列表...")
    if not test_api_endpoints():
        print("❌ 重新检查失败")
        return False
    
    print("✅ 任务同步测试完成")
    return True

def test_frontend_integration():
    """测试前端集成"""
    print("\n🌐 前端集成测试提示:")
    print("1. 打开浏览器访问: http://localhost:3000/tasks")
    print("2. 检查是否能看到任务列表")
    print("3. 尝试切换不同的标签页")
    print("4. 检查任务操作是否正常")
    print("5. 如果看到任务，说明修复成功！")

def main():
    """主函数"""
    print("🚀 任务同步修复测试开始...\n")
    
    # 检查后端服务
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未运行，请先启动后端服务")
            print("   命令: uvicorn api.main:app --reload")
            return
    except requests.exceptions.RequestException:
        print("❌ 无法连接到后端服务，请检查:")
        print("   1. 后端服务是否启动: uvicorn api.main:app --reload")
        print("   2. Redis服务是否运行: redis-server")
        print("   3. RQ Worker是否运行: python start_rq_worker.py")
        return
    
    print("✅ 后端服务连接正常\n")
    
    # 运行测试
    success = test_task_sync()
    
    if success:
        print("\n🎉 任务同步修复测试成功！")
        print("\n现在任务监控器会自动发现RQ中的任务，")
        print("任务管理页面应该能显示所有任务了。")
        
        test_frontend_integration()
    else:
        print("\n❌ 测试失败，可能的原因:")
        print("1. Redis服务未运行")
        print("2. RQ Worker未运行") 
        print("3. 数据库连接问题")
        print("4. 网络连接问题")
        
        print("\n🔧 建议的解决步骤:")
        print("1. 启动Redis: redis-server")
        print("2. 启动RQ Worker: python start_rq_worker.py")
        print("3. 检查日志输出")
        print("4. 运行诊断脚本: python simple_task_check.py")

if __name__ == "__main__":
    main()
