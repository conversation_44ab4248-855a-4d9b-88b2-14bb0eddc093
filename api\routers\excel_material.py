"""
Excel材料处理路由
提供Excel材料上传、处理和结果查询的API接口
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, File, UploadFile, HTTPException, Form, Query
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
import pandas as pd
import numpy as np
import io

from ..core.debug import get_app_logger
from ..core.task_manager import submit_task, get_task_status
from ..tasks.excel_material_processor import (
    process_excel_materials, merge_excel_results, check_excel_task_status,
    cleanup_expired_tasks, EXCEL_STORAGE_DIR, RESULT_STORAGE_DIR
)
from ..routers.excel import process_excel_data, find_end_row

# 获取日志记录器
logger = get_app_logger("excel_material")

# 创建路由
router = APIRouter(
    prefix="/excel-material",
    tags=["excel-material"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class ExcelProcessRequest(BaseModel):
    """Excel处理请求模型"""
    header_row: int = 0
    batch_size: int = 50
    retention_days: int = 30
    description: str = ""

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    task_id: str
    status: str
    progress: float = 0.0
    total_batches: int = 0
    completed_batches: int = 0
    failed_batches: int = 0
    summary: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    expires_at: Optional[str] = None

@router.post("/upload")
async def upload_excel_material(
    file: UploadFile = File(...),
    request_data: str = Form(...)
):
    """
    上传Excel材料文件并启动处理任务
    
    Args:
        file: Excel文件
        request_data: 处理请求参数（JSON字符串）
    
    Returns:
        任务信息
    """
    logger.info(f"收到Excel材料上传请求: {file.filename}")
    
    try:
        # 解析请求参数
        try:
            request_params = json.loads(request_data)
            process_request = ExcelProcessRequest(**request_params)
        except (json.JSONDecodeError, ValueError) as e:
            raise HTTPException(status_code=400, detail=f"请求参数格式错误: {e}")
        
        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="只支持Excel文件格式 (.xlsx, .xls)")
        
        # 读取Excel文件
        contents = await file.read()
        
        try:
            # 使用pandas读取Excel
            df = pd.read_excel(io.BytesIO(contents), header=None)
            df = process_excel_data(df)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Excel文件读取失败: {e}")
        
        # 获取实际的Excel行数
        actual_end_row = find_end_row(df)
        total_rows = actual_end_row + 1
        
        if total_rows <= process_request.header_row:
            raise HTTPException(status_code=400, detail="Excel文件数据不足")
        
        # 提取标题行
        if process_request.header_row >= 0 and process_request.header_row < len(df):
            header_values = df.iloc[process_request.header_row].tolist()
            header_values = [str(val) if pd.notna(val) else f"列{i}" for i, val in enumerate(header_values)]
            
            # 创建数据DataFrame
            data_df = pd.DataFrame(df.iloc[process_request.header_row + 1:].values, columns=header_values)
        else:
            raise HTTPException(status_code=400, detail="标题行索引无效")
        
        # 验证必需的列
        required_columns = ["名称", "数量"]
        missing_columns = []
        for req_col in required_columns:
            if not any(req_col in str(col) for col in header_values):
                missing_columns.append(req_col)
        
        if missing_columns:
            raise HTTPException(
                status_code=400, 
                detail=f"Excel文件缺少必需的列: {missing_columns}。请确保包含：名称、型号、规格、数量、价格、备注"
            )
        
        # 保存原始文件
        file_id = uuid.uuid4().hex[:8]
        original_filename = f"{file_id}_{file.filename}"
        file_path = os.path.join(EXCEL_STORAGE_DIR, original_filename)
        
        with open(file_path, 'wb') as f:
            f.write(contents)
        
        # 准备数据
        data_records = data_df.replace({np.nan: None}).to_dict('records')
        
        # 准备元数据
        metadata = {
            "filename": file.filename,
            "original_filename": original_filename,
            "file_path": file_path,
            "upload_time": datetime.now().isoformat(),
            "file_size": len(contents),
            "total_rows": len(data_records),
            "header_row": process_request.header_row,
            "batch_size": process_request.batch_size,
            "retention_days": process_request.retention_days,
            "description": process_request.description
        }
        
        # 提交主处理任务
        main_task_id = submit_task(
            process_excel_materials,
            file_path,
            header_values,
            data_records,
            metadata,
            queue="high",
            timeout=3600,
            description=f"Excel材料处理: {file.filename}"
        )
        
        logger.info(f"Excel材料处理任务已提交: {main_task_id}")
        
        return {
            "task_id": main_task_id,
            "status": "submitted",
            "message": f"Excel文件已上传，处理任务已启动",
            "file_info": {
                "filename": file.filename,
                "size": len(contents),
                "total_rows": len(data_records),
                "headers": header_values
            },
            "processing_info": {
                "batch_size": process_request.batch_size,
                "estimated_batches": (len(data_records) + process_request.batch_size - 1) // process_request.batch_size,
                "retention_days": process_request.retention_days
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传Excel材料文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"处理文件时出错: {str(e)}")

@router.get("/task/{task_id}/status", response_model=TaskStatusResponse)
async def get_excel_task_status(task_id: str):
    """
    获取Excel处理任务状态
    
    Args:
        task_id: 任务ID
    
    Returns:
        任务状态信息
    """
    logger.info(f"查询Excel任务状态: {task_id}")
    
    try:
        # 首先检查主任务状态
        main_task_status = get_task_status(task_id)
        
        if main_task_status["status"] == "not_found":
            # 尝试检查Excel任务状态
            excel_status = check_excel_task_status(task_id)
            if excel_status["status"] == "not_found":
                raise HTTPException(status_code=404, detail="任务不存在")
            
            return TaskStatusResponse(**excel_status)
        
        # 如果主任务完成，检查详细状态
        if main_task_status["status"] == "finished":
            excel_status = check_excel_task_status(task_id)
            if excel_status["status"] != "not_found":
                return TaskStatusResponse(**excel_status)
        
        # 返回主任务状态
        return TaskStatusResponse(
            task_id=task_id,
            status=main_task_status["status"],
            created_at=main_task_status.get("created_at"),
            summary={"main_task_status": main_task_status["status"]}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询任务状态时出错: {str(e)}")

@router.post("/task/{task_id}/merge")
async def merge_task_results(task_id: str):
    """
    合并任务结果
    
    Args:
        task_id: 任务ID
    
    Returns:
        合并结果
    """
    logger.info(f"开始合并任务结果: {task_id}")
    
    try:
        # 检查任务状态
        task_status = check_excel_task_status(task_id)
        
        if task_status["status"] == "not_found":
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task_status["status"] not in ["ready_for_merge", "partial_complete"]:
            raise HTTPException(
                status_code=400, 
                detail=f"任务状态不允许合并: {task_status['status']}"
            )
        
        # 提交合并任务
        merge_task_id = submit_task(
            merge_excel_results,
            task_id,
            queue="low",
            timeout=600,
            description=f"合并Excel结果: {task_id}"
        )
        
        return {
            "message": "结果合并任务已启动",
            "merge_task_id": merge_task_id,
            "original_task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"合并任务结果失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"合并结果时出错: {str(e)}")

@router.get("/task/{task_id}/download")
async def download_result_file(task_id: str):
    """
    下载处理结果文件
    
    Args:
        task_id: 任务ID
    
    Returns:
        Excel结果文件
    """
    logger.info(f"下载结果文件: {task_id}")
    
    try:
        # 检查结果文件是否存在
        excel_filename = f"{task_id}_result.xlsx"
        excel_path = os.path.join(RESULT_STORAGE_DIR, excel_filename)
        
        if not os.path.exists(excel_path):
            raise HTTPException(status_code=404, detail="结果文件不存在")
        
        return FileResponse(
            path=excel_path,
            filename=f"材料处理结果_{task_id}.xlsx",
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载结果文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下载文件时出错: {str(e)}")

@router.get("/tasks")
async def list_excel_tasks(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    limit: int = Query(50, ge=1, le=100, description="返回数量限制")
):
    """
    获取Excel处理任务列表
    
    Args:
        status: 状态过滤
        limit: 返回数量限制
    
    Returns:
        任务列表
    """
    logger.info(f"获取Excel任务列表，状态过滤: {status}")
    
    try:
        tasks = []
        
        # 扫描结果目录
        if os.path.exists(RESULT_STORAGE_DIR):
            for filename in os.listdir(RESULT_STORAGE_DIR):
                if filename.endswith('.json') and not filename.endswith('_final.json'):
                    try:
                        task_file = os.path.join(RESULT_STORAGE_DIR, filename)
                        with open(task_file, 'r', encoding='utf-8') as f:
                            task_info = json.load(f)
                        
                        task_status_info = check_excel_task_status(task_info.get("task_id", ""))
                        
                        # 状态过滤
                        if status and task_status_info.get("status") != status:
                            continue
                        
                        tasks.append({
                            "task_id": task_info.get("task_id"),
                            "status": task_status_info.get("status"),
                            "filename": task_info.get("metadata", {}).get("filename"),
                            "total_rows": task_info.get("total_rows"),
                            "created_at": task_info.get("created_at"),
                            "expires_at": task_info.get("expires_at"),
                            "progress": task_status_info.get("progress", 0),
                            "summary": task_status_info.get("summary")
                        })
                        
                        if len(tasks) >= limit:
                            break
                            
                    except Exception as e:
                        logger.error(f"读取任务文件失败 {filename}: {e}")
                        continue
        
        # 按创建时间排序
        tasks.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        
        return {
            "tasks": tasks[:limit],
            "total_count": len(tasks),
            "filtered_by_status": status
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务列表时出错: {str(e)}")

@router.delete("/task/{task_id}")
async def delete_excel_task(task_id: str):
    """
    删除Excel处理任务及相关文件
    
    Args:
        task_id: 任务ID
    
    Returns:
        删除结果
    """
    logger.info(f"删除Excel任务: {task_id}")
    
    try:
        deleted_files = []
        
        # 删除任务信息文件
        task_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}.json")
        if os.path.exists(task_file):
            os.remove(task_file)
            deleted_files.append("任务信息文件")
        
        # 删除最终结果文件
        final_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}_final.json")
        if os.path.exists(final_file):
            os.remove(final_file)
            deleted_files.append("最终结果文件")
        
        # 删除Excel结果文件
        excel_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}_result.xlsx")
        if os.path.exists(excel_file):
            os.remove(excel_file)
            deleted_files.append("Excel结果文件")
        
        # 删除原始文件（需要从任务信息中获取路径）
        # 这里简化处理，实际应该从任务信息中读取原始文件路径
        
        if not deleted_files:
            raise HTTPException(status_code=404, detail="任务不存在或已被删除")
        
        return {
            "message": f"任务 {task_id} 已删除",
            "deleted_files": deleted_files
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除任务时出错: {str(e)}")

@router.post("/cleanup")
async def cleanup_expired_excel_tasks():
    """
    清理过期的Excel处理任务
    
    Returns:
        清理结果
    """
    logger.info("开始清理过期的Excel任务")
    
    try:
        cleaned_count = cleanup_expired_tasks()
        
        return {
            "message": f"清理完成，删除了 {cleaned_count} 个过期任务",
            "cleaned_count": cleaned_count
        }
        
    except Exception as e:
        logger.error(f"清理过期任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"清理任务时出错: {str(e)}")

@router.get("/info")
async def get_excel_material_info():
    """
    获取Excel材料处理功能信息
    
    Returns:
        功能信息
    """
    return {
        "title": "Excel材料处理系统",
        "description": "支持Excel材料数据的分批异步处理，包括价格查询和数据库匹配",
        "features": [
            "Excel文件上传和解析",
            "分批异步处理",
            "API价格查询",
            "数据库材料匹配",
            "最终价格计算",
            "结果Excel文件生成",
            "30天数据保留",
            "任务状态监控"
        ],
        "supported_columns": [
            "名称 (必需)",
            "型号/规格",
            "数量 (必需)",
            "价格",
            "备注",
            "品牌",
            "产地",
            "单位"
        ],
        "endpoints": [
            "POST /excel-material/upload - 上传Excel文件",
            "GET /excel-material/task/{task_id}/status - 查询任务状态",
            "POST /excel-material/task/{task_id}/merge - 合并结果",
            "GET /excel-material/task/{task_id}/download - 下载结果",
            "GET /excel-material/tasks - 获取任务列表",
            "DELETE /excel-material/task/{task_id} - 删除任务",
            "POST /excel-material/cleanup - 清理过期任务"
        ]
    }
