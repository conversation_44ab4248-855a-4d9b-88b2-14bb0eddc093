from fastapi import APIRouter, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
import pandas as pd
import numpy as np
import io
import json
from typing import Optional, List
from pydantic import BaseModel
from ..core.debug import get_app_logger
from ..core.dify_client import DifyClient
from ..core.rq_app import default_queue, get_queue, enqueue_job

# 配置日志记录器
logger = get_app_logger(__name__)

# 定义请求模型
class SheetConfig(BaseModel):
    sheet_name: str
    header_row: int = 0
    max_rows: Optional[int] = None

class ExcelReadRequest(BaseModel):
    file_path: str
    sheets: List[SheetConfig]

router = APIRouter(
    prefix="/excel",
    tags=["excel"],
)

def process_excel_data(df: pd.DataFrame):
    """
    处理Excel数据，清洗非法浮点数值并转换数据类型
    """
    # 清洗非法浮点数值
    df = df.replace([np.inf, -np.inf], np.nan)
    df = df.fillna('')

    # 转换数值类型为Python原生类型
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    df[numeric_cols] = df[numeric_cols].astype(object).where(df[numeric_cols].notnull(), None)

    return df

def find_end_row(df: pd.DataFrame) -> int:
    """
    从文件末尾开始查找结束行
    1. 去除结尾空行
    2. 向上查找，直到找到符合条件的行
    """
    # 去除结尾空行
    while len(df) > 0 and df.iloc[-1].isnull().all():
        df = df.iloc[:-1]

    # 从末尾开始查找结束行
    end_row = len(df) - 1
    while end_row >= 0:
        # 如果某行少于2个有效列，继续向上查找
        if df.iloc[end_row].count() < 2:
            end_row -= 1
        else:
            break

    return end_row
@router.put("/read")
async def read_excel(
    file: UploadFile = File(...)

):
    """
    读取Excel文件，根据传入的行数限制返回数据

    参数:
    - file: 上传的Excel文件
    - max_rows: 最大行数限制，如果不指定则返回所有行
    - header_row: 标题行索引，默认为0（第一行）

    返回:
    - 包含Excel数据的JSON响应
    """
    try:
        # 读取Excel文件
        contents = await file.read()
        max_rows = 10000
        header_row = 0
        # 使用pandas读取Excel
        df = pd.read_excel(io.BytesIO(contents), header=None)
        df = process_excel_data(df)

        # 获取实际的Excel行数
        actual_end_row = find_end_row(df)
        total_rows = actual_end_row + 1

        # 如果指定了标题行，则使用标题行作为列名
        if header_row >= 0 and header_row < len(df):
            header_values = df.iloc[header_row].tolist()
            # 将标题行的值转换为字符串
            header_values = [str(val) if pd.notna(val) else f"列{i}" for i, val in enumerate(header_values)]

            # 创建新的DataFrame，使用标题行作为列名
            df_with_header = pd.DataFrame(df.iloc[header_row+1:].values, columns=header_values)
        else:
            # 如果标题行无效，使用默认列名
            df_with_header = df

        # 获取Excel的工作表名称
        sheets = pd.ExcelFile(io.BytesIO(contents)).sheet_names

        # 计算有效数据行数（排除标题行）
        effective_rows = total_rows - (header_row + 1)

        # 默认返回所有数据
        logger.info(f"读取Excel文件 '{file.filename}'，返回所有行: {effective_rows}")

        return {
            "total_rows": total_rows,
            "effective_rows": effective_rows,
            "max_rows": max_rows,
            "headers": df_with_header.columns.tolist(),
            "data": df_with_header.replace({np.nan: None}).to_dict('records'),
            "sheets": sheets,
            "limited_by_max_rows": False,
            "exceeds_limit": False
        }

    except Exception as e:
        logger.error(f"处理Excel文件时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理Excel文件时出错: {str(e)}")

@router.post("/read-multi-sheets")
async def read_multi_sheets(
    file: UploadFile = File(...),
    sheet_configs: str = Form(...)  # JSON字符串，包含工作表配置
):
    """
    读取Excel文件的多个工作表，根据传入的配置返回数据

    参数:
    - file: 上传的Excel文件
    - sheet_configs: JSON字符串，包含工作表配置，格式为：
      [
        {"sheet_name": "Sheet1", "header_row": 0, "max_rows": 100},
        {"sheet_name": "Sheet2", "header_row": 1, "max_rows": 200}
      ]

    返回:
    - 包含多个工作表数据的JSON响应
    """
    try:
        # 解析工作表配置
        configs = json.loads(sheet_configs)

        # 读取Excel文件
        contents = await file.read()
        excel_file = pd.ExcelFile(io.BytesIO(contents))

        # 获取所有工作表名称
        all_sheets = excel_file.sheet_names

        # 存储结果
        result = {
            "file_name": file.filename,
            "all_sheets": all_sheets,
            "sheets_data": []
        }

        # 处理每个工作表
        for config in configs:
            sheet_name = config.get("sheet_name")
            header_row = config.get("header_row", 0)
            max_rows = config.get("max_rows")

            # 检查工作表是否存在
            if sheet_name not in all_sheets:
                result["sheets_data"].append({
                    "sheet_name": sheet_name,
                    "error": f"工作表 '{sheet_name}' 不存在",
                    "exists": False
                })
                continue

            # 读取工作表数据
            df = pd.read_excel(io.BytesIO(contents), sheet_name=sheet_name, header=None)
            df = process_excel_data(df)

            # 获取实际的Excel行数
            actual_end_row = find_end_row(df)
            total_rows = actual_end_row + 1

            # 如果指定了标题行，则使用标题行作为列名
            if header_row >= 0 and header_row < len(df):
                header_values = df.iloc[header_row].tolist()
                # 将标题行的值转换为字符串
                header_values = [str(val) if pd.notna(val) else f"列{i}" for i, val in enumerate(header_values)]

                # 创建新的DataFrame，使用标题行作为列名
                df_with_header = pd.DataFrame(df.iloc[header_row+1:].values, columns=header_values)
            else:
                # 如果标题行无效，使用默认列名
                df_with_header = df

            # 计算有效数据行数（排除标题行）
            effective_rows = total_rows - (header_row + 1)

            # 根据max_rows参数限制返回的行数
            sheet_result = {
                "sheet_name": sheet_name,
                "total_rows": total_rows,
                "effective_rows": effective_rows,
                "max_rows": max_rows,
                "headers": df_with_header.columns.tolist(),
                "exists": True
            }

            # 检查是否超过最大行数限制
            exceeds_limit = False
            limited_by_max_rows = False

            if max_rows is not None and effective_rows > max_rows:
                exceeds_limit = True
                limited_by_max_rows = True
                logger.info(f"读取工作表 '{sheet_name}'，行数超过限制: {effective_rows} > {max_rows}")

                # 返回行数信息，但不返回数据
                sheet_result.update({
                    "limited_by_max_rows": limited_by_max_rows,
                    "exceeds_limit": exceeds_limit,
                    "message": f"工作表行数超过限制: {effective_rows} > {max_rows}"
                })
            else:
                # 默认返回所有数据
                logger.info(f"读取工作表 '{sheet_name}'，返回所有行: {effective_rows}")

                sheet_result.update({
                    "data": df_with_header.replace({np.nan: None}).to_dict('records'),
                    "limited_by_max_rows": limited_by_max_rows,
                    "exceeds_limit": exceeds_limit
                })

            result["sheets_data"].append(sheet_result)

        return result

    except json.JSONDecodeError:
        logger.error(f"解析工作表配置时出错: 无效的JSON格式")
        raise HTTPException(status_code=400, detail="无效的工作表配置格式，请提供有效的JSON")
    except Exception as e:
        logger.error(f"处理多工作表Excel文件时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理多工作表Excel文件时出错: {str(e)}")

@router.post("/read-multi-files")
@router.post("/read-quotation")
async def read_quotation_sheet(
    file: UploadFile = File(...),
    base_info: str = Form(...)  # 基础信息JSON字符串
):
    """
    读取包含"报价单"的第一个sheet，将数据加入队列供dify处理

    参数:
    - file: 上传的Excel文件
    - base_info: 基础信息JSON字符串

    返回:
    - 包含任务ID和sheet信息的JSON响应
    """
    try:
        # 读取Excel文件
        contents = await file.read()
        excel_file = pd.ExcelFile(io.BytesIO(contents))

        # 查找包含"报价单"的sheet
        quotation_sheets = [s for s in excel_file.sheet_names if "报价单" in s]
        if not quotation_sheets:
            raise HTTPException(status_code=400, detail="未找到包含'报价单'的工作表")

        sheet_name = quotation_sheets[0]

        # 读取工作表数据
        df = pd.read_excel(io.BytesIO(contents), sheet_name=sheet_name, header=None)
        df = process_excel_data(df)

        # 获取实际的Excel行数
        actual_end_row = find_end_row(df)
        total_rows = actual_end_row + 1

        # 默认使用第一行作为标题行
        header_row = 0
        if header_row >= 0 and header_row < len(df):
            header_values = df.iloc[header_row].tolist()
            header_values = [str(val) if pd.notna(val) else f"列{i}" for i, val in enumerate(header_values)]
            df_with_header = pd.DataFrame(df.iloc[header_row+1:].values, columns=header_values)
        else:
            df_with_header = df

        # 准备任务数据
        task_data = {
            "file_name": file.filename,
            "sheet_name": sheet_name,
            "headers": df_with_header.columns.tolist(),
            "data": df_with_header.replace({np.nan: None}).to_dict('records'),
            "base_info": json.loads(base_info)
        }

        # 将任务加入队列
        job = default_queue.enqueue(
            "api.tasks.process_quotation_task",
            task_data,
            result_ttl=86400  # 结果保留24小时
        )

        return {
            "task_id": job.get_id(),
            "file_name": file.filename,
            "sheet_name": sheet_name,
            "total_rows": total_rows,
            "headers": df_with_header.columns.tolist()
        }

    except json.JSONDecodeError:
        logger.error(f"解析基础信息时出错: 无效的JSON格式")
        raise HTTPException(status_code=400, detail="无效的基础信息格式，请提供有效的JSON")
    except Exception as e:
        logger.error(f"处理报价单时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理报价单时出错: {str(e)}")

@router.put("/read-multi-files")
async def read_multi_files(
    files: List[UploadFile] = File(...),
    configs: Optional[str] = Form(None)  # JSON字符串，包含每个文件的工作表配置，可选
):
    """
    读取多个Excel文件的多个工作表，根据传入的配置返回数据

    参数:
    - files: 上传的多个Excel文件
    - configs: JSON字符串，包含每个文件的工作表配置，格式为：
      [
        {
          "file_index": 0,
          "sheets": [
            {"sheet_name": "Sheet1", "header_row": 0, "max_rows": 100},
            {"sheet_name": "Sheet2", "header_row": 1, "max_rows": 200}
          ]
        },
        {
          "file_index": 1,
          "sheets": [
            {"sheet_name": "Sheet1", "header_row": 0, "max_rows": 100}
          ]
        }
      ]
      如果不提供此参数，将自动读取所有文件的所有工作表，使用默认标题行（第一行）

    返回:
    - 包含多个文件多个工作表数据的JSON响应
    """
    try:
        # 解析配置或创建默认配置
        file_configs = []
        if configs:
            # 如果提供了配置，解析JSON
            file_configs = json.loads(configs)
        else:
            # 如果没有提供配置，为每个文件创建默认配置
            logger.info(f"未提供配置，将自动读取所有文件的所有工作表")
            for i, file in enumerate(files):
                file_configs.append({
                    "file_index": i,
                    "sheets": []  # 稍后会填充工作表信息
                })

        # 存储结果
        result = {
            "files": []
        }

        # 处理每个文件
        for file_config in file_configs:
            file_index = file_config.get("file_index", 0)
            sheet_configs = file_config.get("sheets", [])

            # 检查文件索引是否有效
            if file_index < 0 or file_index >= len(files):
                result["files"].append({
                    "file_name": f"索引 {file_index}",
                    "error": f"无效的文件索引: {file_index}",
                    "exists": False
                })
                continue

            file = files[file_index]

            # 读取Excel文件
            contents = await file.read()

            try:
                excel_file = pd.ExcelFile(io.BytesIO(contents))
                all_sheets = excel_file.sheet_names
            except Exception as e:
                result["files"].append({
                    "file_name": file.filename,
                    "error": f"无法读取Excel文件: {str(e)}",
                    "exists": True,
                    "is_valid_excel": False
                })
                continue

            # 存储文件结果
            file_result = {
                "file_name": file.filename,
                "exists": True,
                "is_valid_excel": True,
                "all_sheets": all_sheets,
                "sheets_data": []
            }

            # 处理每个工作表
            if sheet_configs:
                # 如果提供了工作表配置，使用配置
                sheet_configs_to_process = sheet_configs
            else:
                # 如果没有提供工作表配置，自动读取所有工作表
                sheet_configs_to_process = [{"sheet_name": sheet_name, "header_row": 0} for sheet_name in all_sheets]
                logger.info(f"文件 '{file.filename}' 未提供工作表配置，将自动读取所有工作表: {all_sheets}")

            for config in sheet_configs_to_process:
                sheet_name = config.get("sheet_name")
                header_row = config.get("header_row", 0)
                max_rows = config.get("max_rows")

                # 检查工作表是否存在
                if sheet_name not in all_sheets:
                    file_result["sheets_data"].append({
                        "sheet_name": sheet_name,
                        "error": f"工作表 '{sheet_name}' 不存在",
                        "exists": False
                    })
                    continue

                # 读取工作表数据
                df = pd.read_excel(io.BytesIO(contents), sheet_name=sheet_name, header=None)
                df = process_excel_data(df)

                # 获取实际的Excel行数
                actual_end_row = find_end_row(df)
                total_rows = actual_end_row + 1

                # 如果指定了标题行，则使用标题行作为列名
                if header_row >= 0 and header_row < len(df):
                    header_values = df.iloc[header_row].tolist()
                    # 将标题行的值转换为字符串
                    header_values = [str(val) if pd.notna(val) else f"列{i}" for i, val in enumerate(header_values)]

                    # 创建新的DataFrame，使用标题行作为列名
                    df_with_header = pd.DataFrame(df.iloc[header_row+1:].values, columns=header_values)
                else:
                    # 如果标题行无效，使用默认列名
                    df_with_header = df

                # 计算有效数据行数（排除标题行）
                effective_rows = total_rows - (header_row + 1)

                # 根据max_rows参数限制返回的行数
                sheet_result = {
                    "sheet_name": sheet_name,
                    "total_rows": total_rows,
                    "effective_rows": effective_rows,
                    "max_rows": max_rows,
                    "headers": df_with_header.columns.tolist(),
                    "exists": True
                }

                # 检查是否超过最大行数限制
                exceeds_limit = False
                limited_by_max_rows = False

                if max_rows is not None and effective_rows > max_rows:
                    exceeds_limit = True
                    limited_by_max_rows = True
                    logger.info(f"读取文件 '{file.filename}' 工作表 '{sheet_name}'，行数超过限制: {effective_rows} > {max_rows}")

                    # 返回行数信息，但不返回数据
                    sheet_result.update({
                        "limited_by_max_rows": limited_by_max_rows,
                        "exceeds_limit": exceeds_limit,
                        "message": f"工作表行数超过限制: {effective_rows} > {max_rows}"
                    })
                else:
                    # 默认返回所有数据
                    logger.info(f"读取文件 '{file.filename}' 工作表 '{sheet_name}'，返回所有行: {effective_rows}")

                    sheet_result.update({
                        "data": df_with_header.replace({np.nan: None}).to_dict('records'),
                        "limited_by_max_rows": limited_by_max_rows,
                        "exceeds_limit": exceeds_limit
                    })

                file_result["sheets_data"].append(sheet_result)

            result["files"].append(file_result)

        return result

    except json.JSONDecodeError:
        # 只有在提供了configs参数但格式无效时才会抛出此错误
        if configs:
            logger.error(f"解析配置时出错: 无效的JSON格式")
            raise HTTPException(status_code=400, detail="无效的配置格式，请提供有效的JSON")
    except Exception as e:
        logger.error(f"处理多文件多工作表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理多文件多工作表时出错: {str(e)}")
