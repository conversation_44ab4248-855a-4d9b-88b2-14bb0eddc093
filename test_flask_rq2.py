#!/usr/bin/env python
"""
Flask-RQ2 测试脚本
用于测试Flask-RQ2的配置和功能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_flask_rq2_import():
    """测试Flask-RQ2导入"""
    print("测试Flask-RQ2导入...")
    try:
        from api.core.rq_app import rq, get_flask_rq, get_queue, job
        print("✓ Flask-RQ2导入成功")
        return True
    except Exception as e:
        print(f"✗ Flask-RQ2导入失败: {e}")
        return False

def test_redis_connection():
    """测试Redis连接"""
    print("测试Redis连接...")
    try:
        from api.core.rq_app import get_redis_connection
        redis_conn = get_redis_connection()
        redis_conn.ping()
        print("✓ Redis连接成功")
        return True
    except Exception as e:
        print(f"✗ Redis连接失败: {e}")
        return False

def test_queue_creation():
    """测试队列创建"""
    print("测试队列创建...")
    try:
        from api.core.rq_app import get_queue
        
        # 测试获取不同队列
        default_queue = get_queue("default")
        high_queue = get_queue("high")
        low_queue = get_queue("low")
        
        print(f"✓ 默认队列: {default_queue.name}")
        print(f"✓ 高优先级队列: {high_queue.name}")
        print(f"✓ 低优先级队列: {low_queue.name}")
        return True
    except Exception as e:
        print(f"✗ 队列创建失败: {e}")
        return False

def test_simple_task():
    """测试简单任务"""
    print("测试简单任务...")
    try:
        from api.core.rq_app import get_queue
        
        # 定义一个简单的测试任务
        def simple_task(name):
            return f"Hello, {name}!"
        
        # 提交任务
        queue = get_queue("default")
        job = queue.enqueue(simple_task, "Flask-RQ2")
        
        print(f"✓ 任务已提交，ID: {job.id}")
        print(f"✓ 任务状态: {job.get_status()}")
        
        # 等待任务完成（最多等待10秒）
        timeout = 10
        start_time = time.time()
        while not job.is_finished and not job.is_failed:
            if time.time() - start_time > timeout:
                print(f"⚠ 任务超时（{timeout}秒）")
                break
            time.sleep(0.1)
        
        if job.is_finished:
            print(f"✓ 任务完成，结果: {job.result}")
            return True
        elif job.is_failed:
            print(f"✗ 任务失败: {job.exc_info}")
            return False
        else:
            print("⚠ 任务仍在进行中")
            return False
            
    except Exception as e:
        print(f"✗ 简单任务测试失败: {e}")
        return False

def test_decorator_task():
    """测试装饰器任务"""
    print("测试装饰器任务...")
    try:
        from api.core.rq_app import job
        
        # 使用装饰器定义任务
        @job(queue='default', timeout=30)
        def decorated_task(message):
            return f"装饰器任务: {message}"
        
        # 提交任务
        job_instance = decorated_task.queue("测试消息")
        
        print(f"✓ 装饰器任务已提交，ID: {job_instance.id}")
        print(f"✓ 任务状态: {job_instance.get_status()}")
        
        # 等待任务完成
        timeout = 10
        start_time = time.time()
        while not job_instance.is_finished and not job_instance.is_failed:
            if time.time() - start_time > timeout:
                print(f"⚠ 任务超时（{timeout}秒）")
                break
            time.sleep(0.1)
        
        if job_instance.is_finished:
            print(f"✓ 装饰器任务完成，结果: {job_instance.result}")
            return True
        elif job_instance.is_failed:
            print(f"✗ 装饰器任务失败: {job_instance.exc_info}")
            return False
        else:
            print("⚠ 装饰器任务仍在进行中")
            return False
            
    except Exception as e:
        print(f"✗ 装饰器任务测试失败: {e}")
        return False

def test_queue_info():
    """测试队列信息"""
    print("测试队列信息...")
    try:
        from api.core.rq_app import get_flask_rq
        
        rq = get_flask_rq()
        
        for queue_name in ['default', 'high', 'low']:
            queue = rq.get_queue(queue_name)
            print(f"✓ 队列 {queue_name}:")
            print(f"  - 长度: {len(queue)}")
            print(f"  - 是否为空: {queue.is_empty()}")
            print(f"  - 任务ID列表: {queue.job_ids[:5]}...")  # 只显示前5个
        
        return True
    except Exception as e:
        print(f"✗ 队列信息测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Flask-RQ2 配置测试")
    print("=" * 50)
    
    tests = [
        test_flask_rq2_import,
        test_redis_connection,
        test_queue_creation,
        test_simple_task,
        test_decorator_task,
        test_queue_info
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 30)
    
    print()
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！Flask-RQ2配置正确。")
        return True
    else:
        print("✗ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
