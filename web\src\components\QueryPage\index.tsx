import { Table, Form, Input, Button, Space, Select } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';
import useSWR from 'swr';
import axios from 'axios';

interface Material {
  id: number;
  product_name: string;
  specification: string;
  brand: string;
  origin: string;
  quantity: number;
  unit: string;
  unit_price: number;
  amount: number;
  remarks: string;
  created_at: string;
  updated_at: string;
  warranty_months?: number; // 添加质量保修月份
  includes_delivery_installation?: boolean; // 添加是否包含送货安装
}

const fetcher = (url: string) => axios.get(url).then(res => res.data);

export default function QueryPage() {
  const [form] = Form.useForm();
  const [queryParams, setQueryParams] = useState({
    skip: 0,
    limit: 10,
    product_name: '',
    specification: '',
    brand: '',
    origin: '',
    sort_by: 'id',
    sort_order: 'asc'
  });

  const buildQueryString = () => {
    const params = new URLSearchParams({
      skip: queryParams.skip.toString(),
      limit: queryParams.limit.toString(),
      sort_by: queryParams.sort_by,
      sort_order: queryParams.sort_order
    });

    if (queryParams.product_name) {
      params.set('product_name', queryParams.product_name);
    }
    if (queryParams.specification) {
      params.set('specification', queryParams.specification);
    }
    if (queryParams.brand) {
      params.set('brand', queryParams.brand);
    }
    if (queryParams.origin) {
      params.set('origin', queryParams.origin);
    }

    return `/api/items/?${params.toString()}`;
  };

  const { data, error, isLoading } = useSWR(buildQueryString(), fetcher, {
    keepPreviousData: true
  });

  const handleSearch = (values: Record<string, any>) => {
    setQueryParams(prev => ({
      ...prev,
      skip: 0,
      product_name: values.product_name || '',
      specification: values.specification || '',
      brand: values.brand || '',
      origin: values.origin || '',
      sort_by: values.sort_by || 'id',
      sort_order: values.sort_order || 'asc'
    }));
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQueryParams(prev => ({
      ...prev,
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      sort_by: sorter.field || 'id',
      sort_order: sorter.order === 'ascend' ? 'asc' : 'desc'
    }));
  };

  const columns: ColumnsType<Material> = [
    { title: '产品名称', dataIndex: 'product_name', key: 'product_name', sorter: true },
    { title: '规格型号', dataIndex: 'specification', key: 'specification', sorter: true },
    { title: '品牌', dataIndex: 'brand', key: 'brand', sorter: true },
    { title: '产地', dataIndex: 'origin', key: 'origin', sorter: true },
    { title: '数量', dataIndex: 'quantity', key: 'quantity', sorter: true },
    { title: '单位', dataIndex: 'unit', key: 'unit' },
    { title: '单价', dataIndex: 'unit_price', key: 'unit_price', sorter: true },
    { title: '金额', dataIndex: 'amount', key: 'amount', sorter: true },
    { title: '备注', dataIndex: 'remarks', key: 'remarks' },
    { title: '保修(月)', dataIndex: 'warranty_months', key: 'warranty_months', sorter: true },
    { 
      title: '含送装', 
      dataIndex: 'includes_delivery_installation', 
      key: 'includes_delivery_installation', 
      sorter: true,
      render: (value: boolean | null | undefined) => {
        if (value === true) return '是';
        if (value === false) return '否';
        return '-'; // 或者其他表示空值的字符
      } 
    },
  ];

  return (
    <div>
      <Form form={form} layout="inline" onFinish={handleSearch}>
        <Form.Item label="产品名称" name="product_name">
          <Input placeholder="请输入" />
        </Form.Item>
        
        <Form.Item label="规格型号" name="specification">
          <Input placeholder="请输入" />
        </Form.Item>
        
        <Form.Item label="品牌" name="brand">
          <Input placeholder="请输入" />
        </Form.Item>
        
        <Form.Item label="产地" name="origin">
          <Input placeholder="请输入" />
        </Form.Item>
        
        <Form.Item label="排序字段" name="sort_by">
          <Select
            defaultValue="id"
            options={[
              { value: 'id', label: '默认' },
              { value: 'product_name', label: '产品名称' },
              { value: 'specification', label: '规格型号' },
              { value: 'brand', label: '品牌' },
              { value: 'origin', label: '产地' },
              { value: 'unit_price', label: '单价' },
              { value: 'amount', label: '金额' },
              { value: 'warranty_months', label: '保修(月)' },
              { value: 'includes_delivery_installation', label: '含送装' },
            ]}
          />
        </Form.Item>
        
        <Form.Item label="排序顺序" name="sort_order">
          <Select
            defaultValue="asc"
            options={[
              { value: 'asc', label: '升序' },
              { value: 'desc', label: '降序' },
            ]}
          />
        </Form.Item>
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={isLoading}>
              查询
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>

      <Table
        style={{ marginTop: 24 }}
        columns={columns}
        dataSource={data?.items || []}
        rowKey="id"
        bordered
        loading={isLoading}
        pagination={{
          current: queryParams.skip / queryParams.limit + 1,
          pageSize: queryParams.limit,
          total: data?.total || 0,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        onChange={handleTableChange}
      />
    </div>
  );
}
