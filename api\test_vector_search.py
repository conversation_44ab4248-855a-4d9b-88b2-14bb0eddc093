from .core.vector_db import get_vector_db_client

def test_vector_search():
    # 初始化 VectorDBClient
    vector_db_client = get_vector_db_client()

    # 调用 search_items 方法，使用 "灭火器" 作为输入字符串
    query_text = "灯"
    results = vector_db_client.search_items(query_text,5,0.6,sparse_weight=0.2,dense_weight=0.8)

    # 输出检索结果和得分
    print("检索结果：")
    for result in results:
        print(f"ID: {result['id']}")
        print(f"产品名称: {result['product_name']}")
        print(f"得分: {result['score']}")
        print("-" * 30)

if __name__ == "__main__":
    test_vector_search()
