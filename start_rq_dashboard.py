#!/usr/bin/env python
"""
RQ Dashboard启动脚本
用于启动RQ Dashboard监控界面
"""

import os
import sys
from rq_dashboard import app

# 配置RQ Dashboard
app.config.from_object('rq_dashboard.default_settings')

# 设置Redis连接
app.config['RQ_DASHBOARD_REDIS_URL'] = 'redis://:yhb25IEz@***********:6379/3'

# 设置监听地址和端口
host = '0.0.0.0'
port = 9181

# 启动RQ Dashboard
if __name__ == '__main__':
    print(f"启动RQ Dashboard监控界面...")
    print(f"访问地址: http://localhost:{port}")
    print(f"Redis URL: {app.config['RQ_DASHBOARD_REDIS_URL']}")
    print(f"按Ctrl+C停止服务")

    # 启动Flask应用
    app.run(host=host, port=port, debug=True)
