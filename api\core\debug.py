import os 
import json
from typing import Optional, Dict, Any
from .logging_config import configure_logging, get_logger

# 配置日志
logger = configure_logging(True)

# 获取指定名称的日志记录器
def get_app_logger(name: str):
    return get_logger(name)

# API调试模式默认设置
API_DEBUG_MODE = os.getenv("API_DEBUG_MODE", "False").lower() == "true"

def enable_api_debug(enabled: bool = True):
    """启用或禁用API调试模式
    
    Args:
        enabled: 是否启用API调试模式，默认为True
    
    Returns:
        bool: 当前的API调试模式状态
    """
    from .api_client import APIClient
    APIClient.set_debug_mode(enabled)
    logger.info(f"API调试模式已{'启用' if enabled else '禁用'}")
    return enabled

def format_api_data(data: Any, max_length: int = 1000) -> str:
    """格式化API数据用于日志记录
    
    Args:
        data: 要格式化的数据
        max_length: 最大长度限制，超过则截断
        
    Returns:
        str: 格式化后的字符串
    """
    try:
        if isinstance(data, str):
            result = data
        else:
            result = json.dumps(data, ensure_ascii=False, indent=2)
            
        if len(result) > max_length:
            return f"{result[:max_length]}...(截断，完整长度:{len(result)})"
        return result
    except Exception as e:
        return f"无法格式化数据: {str(e)}"