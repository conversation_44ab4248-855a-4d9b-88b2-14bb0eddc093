from sqlalchemy.orm import Session
import pandas as pd
from ..schemas.item import MaterialsBase, MaterialsCreate, Materials as SchemaMaterials
from ..models.item import Materials
from ..core.vector_db import get_vector_db_client
from ..config import SEARCH_SCORE_THRESHOLD
from io import BytesIO 
from ..config import ENABLE_SEMANTIC_SEARCH
from ..core.logging_config import get_logger
import asyncio

async def get_items_by_semantic_search(query: str = None, model: str = None, unit_price: float = None, limit: int = 10):
    """
    使用语义搜索从向量数据库中获取相关设备项
    
    Args:
        query: 搜索查询文本
        model: 商品型号
        unit_price: 商品单价
        limit: 返回结果数量限制
        
    Returns:
        list: 搜索结果列表
    """

    
    logger = get_logger("item_crud")
    
    # 如果未启用语义搜索，直接返回空列表
    if not ENABLE_SEMANTIC_SEARCH:
        logger.info("语义搜索功能已禁用")
        return []
    
    # 构建搜索查询
    search_text = ""
    if query:
        search_text = query
    elif model:
        search_text = f"型号: {model}"
        if unit_price is not None:
            search_text += f", 单价: {unit_price}"
    else:
        logger.warning("语义搜索缺少必要参数")
        return []
        
    try:
        vector_db_client = get_vector_db_client()
        results = await vector_db_client.search_items(search_text, limit=limit, threshold=SEARCH_SCORE_THRESHOLD,sparse_weight=0.4,dense_weight=0.6)
        
        # 记录搜索结果
        if results:
            logger.info(f"语义搜索成功，找到{len(results)}条结果")
        else:
            logger.info(f"语义搜索未找到匹配结果，查询: {search_text}")
            
        return results
    except Exception as e:
        logger.error(f"语义搜索出错: {e}")
        return []

# 将 Excel 列名映射到数据库模型字段名
COLUMN_MAPPING = {
    '序号': 'serial_number',
    '产品名称': 'product_name',
    '规格型号': 'specification',
    '品牌': 'brand',
    '产地': 'origin',
    '数量': 'quantity',
    '单位': 'unit',
    '单价': 'unit_price',
    '金额': 'amount',
    '备注': 'remarks'
}

def create_equipment_item(db: Session, item: MaterialsCreate):
    """
    在数据库中创建一个设备项。
    """
    db_item = Materials(**item.model_dump()) # 使用 Pydantic V2 的 model_dump()
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

async def bulk_create_items_from_excel(db: Session, file_content: bytes):
    """
    从 Excel 文件内容批量创建设备项。
    假设 Excel 文件第二行是标题行。
    """
    try:
        # 使用 BytesIO 读取内存中的文件内容
        excel_file = BytesIO(file_content)
        # 从第二行开始读取 (header=1)
        df = pd.read_excel(excel_file, header=1)

        # 重命名列以匹配数据库模型字段
        df.rename(columns=COLUMN_MAPPING, inplace=True)

        # 过滤掉无效的列名（如果 Excel 中有未映射的列）
        valid_columns = [col for col in COLUMN_MAPPING.values() if col in df.columns]
        df_filtered = df[valid_columns]

        # 将 NaN 值替换为 None，以便 SQLAlchemy 处理
        df_filtered = df_filtered.where(pd.notna(df_filtered), None)

        # 将 DataFrame 转换为字典列表
        # 将 DataFrame 转换为字典列表，使用显式类型注解
        from typing import Any
        items_data: list[dict[str, Any]] = df_filtered.to_dict(orient='records') # type: ignore

        items_created_count = 0
        items_skipped_count = 0
        errors = []

        for item_data in items_data:
            # 简单的数据验证：例如，确保 'product_name' 不为空
            if not item_data.get('product_name'):
                items_skipped_count += 1
                errors.append(f"Skipped row due to missing product_name: {item_data}")
                continue

            # 清理数据类型（Pandas 可能读取为 numpy 类型）
            for key, value in item_data.items():
                if pd.isna(value):
                    item_data[key] = None
                elif isinstance(value, (int, float)):
                     # 检查是否为整数类型，如果是则转为 int
                    if key in ['serial_number'] and value == int(value):
                         item_data[key] = int(value)
                    # 其他数值类型保持 float
                    elif key in ['quantity', 'unit_price', 'amount']:
                         item_data[key] = float(value)
                elif isinstance(value, str):
                    item_data[key] = str(value) # 确保是 Python str

            try:
                # 确保从schemas模块正确导入MaterialCreate类
                item_schema = MaterialsCreate(**item_data)
                db_item = Materials(**item_schema.model_dump())
                db.add(db_item)
                items_created_count += 1
            except Exception as e:
                items_skipped_count += 1
                errors.append(f"Error processing row {item_data}: {e}")
                db.rollback() # 回滚当前失败的行

        db.commit() # 提交所有成功的行
        
        # 同步到向量数据库
        try:
            vector_db_client = get_vector_db_client()
            # 获取所有新创建的记录
            new_items = db.query(Materials).order_by(Materials.id.desc()).limit(items_created_count).all()
            
            sync_success = 0
            sync_errors = []
            
            # 添加重试逻辑
            from tenacity import retry, wait_exponential, stop_after_attempt
            
            @retry(wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3))
            async def sync_with_retry(item):
                try:
                    item_data = {
                        "id": item.id,
                        "product_name": item.product_name,
                        "specification": item.specification,
                        "brand": item.brand,
                        "origin": item.origin,
                        "warranty_months": item.warranty_months, # 添加 warranty_months
                        "includes_delivery_installation": item.includes_delivery_installation, # 添加 includes_delivery_installation
                    }
                    # 确保 vector_db_client.upsert_item 是异步调用的
                    if await vector_db_client.upsert_item(item_data):
                        return True
                except Exception as e:
                    print(f"同步条目ID {item.id} 失败: {str(e)}")
                    raise
            
            # 使用 asyncio.gather 并发执行所有同步任务
            results = await asyncio.gather(
                *[sync_with_retry(item) for item in new_items],
                return_exceptions=True
            )
            
            for item, result in zip(new_items, results):
                if isinstance(result, Exception):
                    sync_errors.append({
                        "item_id": item.id,
                        "error": str(result),
                        "data": {
                            "product_name": item.product_name,
                            "specification": item.specification
                        }
                    })
                elif result:
                    sync_success += 1
            
            print(f"向量数据库同步完成: 总计{items_created_count}条，成功{sync_success}条")
        except Exception as e:
            # 记录错误但不影响主流程
            print(f"同步到向量数据库时出错: {e}")
        
        return {
            "created": items_created_count,
            "skipped": items_skipped_count,
            "errors": errors
        }

    except Exception as e:
        db.rollback() # 如果读取或处理过程中出错，回滚所有操作
        raise ValueError(f"处理 Excel 文件时出错: {e}")

def get_items(db: Session, skip: int = 0, limit: int = 10,
              product_name: str | None = None,
              specification: str | None = None,
              brand: str | None = None,
              origin: str | None = None,
              sort_by: str | None = None,
              sort_order: str = "asc"):
    """
    从数据库获取物料列表，支持过滤、分页和排序。
    """
    query = db.query(Materials)

    # 应用过滤条件
    if product_name:
        query = query.filter(Materials.product_name.ilike(f"%{product_name}%")) # 使用 ilike 进行不区分大小写的模糊匹配
    if specification:
        query = query.filter(Materials.specification.ilike(f"%{specification}%"))
    if brand:
        query = query.filter(Materials.brand.ilike(f"%{brand}%"))
    if origin:
        query = query.filter(Materials.origin.ilike(f"%{origin}%"))

    # 获取总数（在应用排序和分页之前）
    total = query.count()

    # 应用排序
    if sort_by:
        column = getattr(Materials, sort_by, None)
        if column:
            if sort_order.lower() == "desc":
                query = query.order_by(column.desc())
            else:
                query = query.order_by(column.asc())
    else:
         # 默认按 id 升序排序
        query = query.order_by(Materials.id.asc())


    # 应用分页
    items = query.offset(skip).limit(limit).all()

    return {"items": items, "total": total}
