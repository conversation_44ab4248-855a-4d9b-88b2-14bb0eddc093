from sqlalchemy import Column, DateTime, Integer, String, Float, Text, func, Boolean # 添加 Boolean
from ..core.database import Base # 导入 Base

class Materials(Base):
    __tablename__ = "materials"  # 修改表名为materials

    id = Column(Integer, primary_key=True, index=True, autoincrement=True) # 自增主键
    #serial_number = Column(Integer, index=True, comment="序号") # 对应 Excel 的 '序号'
    product_name = Column(String(255), index=True, comment="产品名称") # 增加长度限制
    contract_id = Column(String(50), nullable=True, comment="合同ID")
    contract_number = Column(String(100), nullable=True, comment="合同编号")
    specification = Column(String(255), comment="规格型号")
    brand = Column(String(100), comment="品牌")
    origin = Column(String(100), comment="产地")
    quantity = Column(Float, comment="数量")
    unit = Column(String(50), comment="单位")
    unit_price = Column(Float, comment="单价")
    amount = Column(Float, comment="金额")
    remarks = Column(Text, nullable=True, comment="备注") # 使用 Text 允许更长备注，允许为空
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    is_synced_to_milvus = Column(Boolean, default=False, nullable=False, index=True, comment="是否已同步到Milvus")
    warranty_months = Column(Integer, nullable=True, comment="质量保修月份")
    includes_delivery_installation = Column(String(50), nullable=True, comment="是否包含送货安装")

    def __repr__(self):
        return f"<Materials(id={self.id}, name='{self.product_name}')>"
