"""change_includes_delivery_installation_to_string

Revision ID: 2657c2b2cb3c
Revises: eef3202e7abf
Create Date: 2025-04-16 14:48:37.627030

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2657c2b2cb3c'
down_revision: Union[str, None] = 'eef3202e7abf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
