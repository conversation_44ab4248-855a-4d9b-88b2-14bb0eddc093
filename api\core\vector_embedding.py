# 向量生成和嵌入模块

import asyncio
from typing import Dict, List, Union, Optional
from nomic import embed
from ..config import (
    EMBEDDING_API_URL, EMBEDDING_API_KEY, EMBEDDING_MODEL,
    USE_LOCAL_EMBEDDING, NOMIC_MODEL_NAME, USE_NOMIC_EMBEDDING
)
from ..core.logging_config import get_logger
from ..core import globals

logger = get_logger("vector_embedding")

class VectorEmbedding:
    """向量生成类，支持本地和API生成向量"""

    @staticmethod
    async def generate_hybrid_embedding(text: str) -> Optional[Dict[str, Union[List[float], Dict]]]:
        """使用配置的模型生成稠密和稀疏嵌入向量"""
        try:
            # 优先使用本地 BGE-M3 (如果已加载)
            if USE_LOCAL_EMBEDDING:
                if globals.embedding_function is not None:
                    logger.debug("使用全局预加载的 BGE-M3 模型生成混合嵌入")
                    
                    
                    embedding_result = globals.embedding_function.encode([text], return_dense=True, return_sparse=True)
                    # 使用全局模型实例的encode方法
                    #embedding_result = await asyncio.to_thread(globals.embedding_function.encode, [text], return_dense=True, return_sparse=True) # 使用encode方法
                    

                    # 处理encode方法返回的结果
                    if isinstance(embedding_result, dict) and 'dense_vecs' in embedding_result  : #and ('sparse_vecs' in embedding_result  or "lexical_weights" in embedding_result)
                        # 转换为期望的格式
                        result = {
                            'dense': embedding_result['dense_vecs'][0],  # 取第一个结果（因为只有一个输入）
                        
                        }
                        
                     
                        logger.debug("使用全局 BGE-M3 生成混合嵌入成功")
                        return result
                    else:
                        logger.error(f"全局 BGE-M3 嵌入函数未返回预期的dict包含'dense_vecs'和'sparse_vecs': {embedding_result}")
                        return None
                else:
                    logger.error("USE_LOCAL_EMBEDDING=True 但全局 BGE-M3 模型未加载或加载失败")
                    return None
            elif USE_NOMIC_EMBEDDING:
                logger.warning("Nomic embedding可能不支持混合搜索所需的稀疏向量")
                dense_embedding = VectorEmbedding.nomic_generate_embedding(text)
                return {"dense": dense_embedding, "sparse": {}} if dense_embedding else None
            else:
                logger.warning("APIClient embedding可能不支持混合搜索所需的稀疏向量")
                from .api_client import APIClient
                dense_embedding = APIClient.generate_embedding(text, EMBEDDING_MODEL)
                return {"dense": dense_embedding, "sparse": {}} if dense_embedding else None

        except Exception as e:
            logger.error(f"生成混合嵌入向量时出错: {e}")
            return None

    @staticmethod
    def generate_embedding(text: str, model_name: str) -> list:
        """生成嵌入向量（仅稠密），可选择使用本地计算或API计算"""
        if USE_NOMIC_EMBEDDING:
            return VectorEmbedding.nomic_generate_embedding(text)
        elif USE_LOCAL_EMBEDDING:
            # 旧逻辑只返回 dense
            hybrid_result = VectorEmbedding.generate_hybrid_embedding(text)
            return hybrid_result['dense'] if hybrid_result else []
        else:
            from .api_client import APIClient
            return APIClient.generate_embedding(text, model_name)

    @staticmethod
    def local_generate_embedding(text: str) -> list:
        """使用本地CPU进行向量计算 (仅稠密)"""
        hybrid_result = VectorEmbedding.generate_hybrid_embedding(text)
        return hybrid_result['dense'] if hybrid_result else []

    @staticmethod
    def nomic_generate_embedding(text: str) -> list:
        """使用nomic模型进行向量计算 (仅稠密)"""
        try:
            output = embed.text(texts=[text], model=NOMIC_MODEL_NAME, task_type='search_document')
            return output['embeddings'][0]
        except Exception as e:
            logger.error(f"nomic模型向量计算失败: {e}")
            return []