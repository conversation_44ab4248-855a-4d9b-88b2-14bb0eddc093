[{"key": "ctrl+shift+a", "command": "workbench.action.tasks.runTask", "args": "启动API服务"}, {"key": "ctrl+shift+w", "command": "workbench.action.tasks.runTask", "args": "启动Celery Worker"}, {"key": "ctrl+shift+b", "command": "workbench.action.tasks.runTask", "args": "启动Celery Beat"}, {"key": "ctrl+shift+f", "command": "workbench.action.tasks.runTask", "args": "启动Flower"}, {"key": "ctrl+shift+v", "command": "workbench.action.tasks.runTask", "args": "启动Web前端"}, {"key": "ctrl+shift+1", "command": "workbench.action.tasks.runTask", "args": "启动API + Worker"}, {"key": "ctrl+shift+2", "command": "workbench.action.tasks.runTask", "args": "启动API + Worker + Beat"}, {"key": "ctrl+shift+3", "command": "workbench.action.tasks.runTask", "args": "启动API + Worker + Flower"}, {"key": "ctrl+shift+4", "command": "workbench.action.tasks.runTask", "args": "启动API + Worker + Beat + Flower"}, {"key": "ctrl+shift+5", "command": "workbench.action.tasks.runTask", "args": "启动所有服务"}]