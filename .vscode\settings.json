{"python.languageServer": "None", "python.analysis.extraPaths": ["${workspaceFolder}"], "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": true, "python.formatting.provider": "autopep8", "editor.formatOnSave": true, "files.exclude": {"**/__pycache__": true, "**/.pytest_cache": true, "**/*.pyc": true}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}"}}