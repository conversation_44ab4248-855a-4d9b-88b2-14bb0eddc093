import React from 'react';
import { <PERSON>, Row, Col, Statistic, Button, Typography, Space } from 'antd';
import { FileExcelOutlined, SearchOutlined, DashboardOutlined, DatabaseOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import './style.css';

const { Title, Paragraph } = Typography;

export default function HomePage() {
  // 模拟的统计数据
  const stats = {
    totalMaterials: 1280,
    categories: 24,
    recentImports: 56
  };

  return (
    <div className="home-page">
      <div className="welcome-section">
        <Title level={2}>欢迎使用材料管理系统</Title>
        <Paragraph>
          本系统提供材料数据的导入、查询和管理功能，帮助您高效管理设备材料信息。
        </Paragraph>
      </div>

      <Row gutter={[24, 24]} className="stats-row">
        <Col xs={24} sm={8}>
          <Card>
            <Statistic 
              title="材料总数" 
              value={stats.totalMaterials} 
              prefix={<DatabaseOutlined />} 
              valueStyle={{ color: '#1677ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic 
              title="材料分类" 
              value={stats.categories} 
              prefix={<DashboardOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic 
              title="近期导入" 
              value={stats.recentImports} 
              prefix={<FileExcelOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]} className="feature-row">
        <Col xs={24} md={12}>
          <Card title="数据导入" className="feature-card">
            <div className="feature-content">
              <FileExcelOutlined className="feature-icon" />
              <div className="feature-text">
                <Paragraph>
                  支持Excel文件导入，自动解析设备材料清单，快速录入大量数据。
                </Paragraph>
                <Link to="/import">
                  <Button type="primary">前往导入</Button>
                </Link>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="数据查询" className="feature-card">
            <div className="feature-content">
              <SearchOutlined className="feature-icon" />
              <div className="feature-text">
                <Paragraph>
                  提供多条件查询功能，快速找到所需材料信息，支持按名称、型号等条件筛选。
                </Paragraph>
                <Link to="/query">
                  <Button type="primary">前往查询</Button>
                </Link>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}