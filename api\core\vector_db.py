# 向量数据库连接和操作模块

from typing import List, Dict, Any, Optional
from ..config import VECTOR_DB_TYPE
from ..core.logging_config import get_logger
from .vector_db_init import VectorDBInitializer
from .vector_db_milvus import MilvusClient
from .vector_db_weaviate import WeaviateClient

logger = get_logger("vector_db")

# 全局变量用于存储单例实例
_vector_db_client = None

def get_vector_db_client() -> 'VectorDBClient':
    """获取向量数据库客户端的单例实例"""
    global _vector_db_client
    if _vector_db_client is None:
        _vector_db_client = VectorDBClient()
    return _vector_db_client

class VectorDBClient:
    """向量数据库客户端，支持Milvus和Weaviate"""

    def __init__(self):
        self.db_type = VECTOR_DB_TYPE
        self.dense_vector_field = "name_vector"  # 稠密向量字段名
        self.sparse_vector_field = "sparse_vector"  # 稀疏向量字段名

        if self.db_type == "milvus":
            # 初始化Milvus
            self.milvus_modules = VectorDBInitializer.init_milvus()
            self.collection, self.primary_field_name = VectorDBInitializer.init_milvus_collection(
                self.milvus_modules,
                self.dense_vector_field,
                self.sparse_vector_field
            )
            self.client = MilvusClient(
                self.collection,
                self.primary_field_name,
                self.dense_vector_field,
                self.sparse_vector_field
            )

        elif self.db_type == "weaviate":
            # 初始化Weaviate
            self.weaviate_client = VectorDBInitializer.init_weaviate()
            VectorDBInitializer.init_weaviate_class(self.weaviate_client)
            self.client = WeaviateClient(self.weaviate_client)

        else:
            raise ValueError(f"不支持的向量数据库类型: {self.db_type}")

    async def upsert_item(self, item_data: dict) -> bool:
        """向向量数据库中插入或更新一条记录"""
        return await self.client.upsert_item(item_data)

    async def delete_item(self, item_id: int) -> bool:
        """从向量数据库中删除一条记录"""
        return await self.client.delete_item(item_id)

    async def search_items(self, query_text: str, limit: int = 10, threshold: float = None, sparse_weight: float = 0.5, dense_weight: float = 0.5) -> List[Dict[str, Any]]:
        """在向量数据库中执行搜索
        
        Args:
            query_text: 查询文本
            limit: 返回结果数量限制
            threshold: 相似度阈值
            sparse_weight: 稀疏向量权重，默认0.5
            dense_weight: 稠密向量权重，默认0.5
        """
        return await self.client.search_items(query_text, limit, threshold, sparse_weight=sparse_weight, dense_weight=dense_weight)
