# api/core/globals.py
from typing import Optional
# 假设 BGEM3EmbeddingFunction 的类型，如果导入路径不同需要调整
# 注意：实际导入可能需要根据你的项目结构调整
try:
    from milvus_model.hybrid import BGEM3EmbeddingFunction
except ImportError:
    # 如果直接从 FlagEmbedding 使用
    try:
        from FlagEmbedding import BGEM3FlagModel as BGEM3EmbeddingFunction # 假设 FlagEmbedding 中是这个名字
    except ImportError:
        BGEM3EmbeddingFunction = None # 或者定义一个占位符类型
        print("警告：无法导入 BGEM3EmbeddingFunction 或 BGEM3FlagModel")


embedding_function: Optional[BGEM3EmbeddingFunction] = None
embedding_dimension: Optional[int] = None # 同时存储维度信息
