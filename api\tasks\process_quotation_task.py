"""处理报价单任务"""
import json
import logging
from typing import Dict, List
from ..core.dify_client import DifyClient
from ..core.logging_config import get_logger

logger = get_logger("quotation_task")

async def process_quotation_task(task_data: Dict):
    """处理报价单任务
    
    Args:
        task_data: 包含报价单数据和基础信息的字典
            {
                "file_name": str,
                "sheet_name": str,
                "headers": List[str],
                "data": List[Dict],
                "base_info": Dict
            }
    """
    try:
        logger.info(f"开始处理报价单: {task_data['file_name']} - {task_data['sheet_name']}")
        
        # 处理每一行数据
        results = []
        for row in task_data["data"]:
            # 检查是否有价格列
            has_price = any("价格" in h for h in task_data["headers"])
            
            if has_price:
                # 价格验证任务
                result = await _validate_price(row, task_data["headers"], task_data["base_info"])
            else:
                # 价格填充任务
                result = await _fill_price(row, task_data["headers"], task_data["base_info"])
            
            results.append(result)
        
        logger.info(f"报价单处理完成: {task_data['file_name']} - {task_data['sheet_name']}")
        return {
            "status": "success",
            "file_name": task_data["file_name"],
            "sheet_name": task_data["sheet_name"],
            "results": results
        }
        
    except Exception as e:
        logger.error(f"处理报价单失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "file_name": task_data["file_name"],
            "sheet_name": task_data["sheet_name"]
        }

async def _validate_price(row: Dict, headers: List[str], base_info: Dict) -> Dict:
    """验证价格是否正确
    
    Args:
        row: 单行数据
        headers: 表头列表
        base_info: 基础信息
        
    Returns:
        包含验证结果的字典
    """
    # 构建查询
    price_col = next(h for h in headers if "价格" in h)
    query = f"""
    请验证以下报价单项目的价格是否正确:
    - 项目: {row.get('项目名称', '未知')}
    - 规格: {row.get('规格', '未知')}
    - 数量: {row.get('数量', '未知')}
    - 报价价格: {row.get(price_col, '未知')}
    
    基础信息:
    {json.dumps(base_info, indent=2, ensure_ascii=False)}
    
    请分析并返回:
    1. 价格是否合理
    2. 合理价格范围
    3. 分析依据
    """
    
    # 调用Dify API
    response = ""
    async for chunk in DifyClient.chat(query, response_mode="blocking"):
        response += chunk
    
    try:
        result = json.loads(response)
        return {
            "row_data": row,
            "validation_result": result,
            "status": "validated"
        }
    except json.JSONDecodeError:
        return {
            "row_data": row,
            "validation_result": response,
            "status": "error",
            "error": "无法解析API响应"
        }

async def _fill_price(row: Dict, headers: List[str], base_info: Dict) -> Dict:
    """填充最接近价格
    
    Args:
        row: 单行数据
        headers: 表头列表
        base_info: 基础信息
        
    Returns:
        包含填充结果的字典
    """
    # 构建查询
    query = f"""
    请为以下报价单项目推荐最接近的价格:
    - 项目: {row.get('项目名称', '未知')}
    - 规格: {row.get('规格', '未知')}
    - 数量: {row.get('数量', '未知')}
    
    基础信息:
    {json.dumps(base_info, indent=2, ensure_ascii=False)}
    
    请分析并返回:
    1. 推荐价格
    2. 价格依据
    3. 价格来源参考
    """
    
    # 调用Dify API
    response = ""
    async for chunk in DifyClient.chat(query, response_mode="blocking"):
        response += chunk
    
    try:
        result = json.loads(response)
        return {
            "row_data": row,
            "filled_price": result,
            "status": "filled"
        }
    except json.JSONDecodeError:
        return {
            "row_data": row,
            "filled_price": None,
            "status": "error",
            "error": "无法解析API响应"
        }
