from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from ..config import DATABASE_URL # 从上级目录的 config 导入

# 创建数据库引擎
# connect_args={"check_same_thread": False} 仅在 SQLite 时需要
# engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False}) # 如果是 SQLite
engine = create_engine(DATABASE_URL)

# 创建数据库会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建 Base 类，用于模型继承
Base = declarative_base()

# 依赖项：获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
def init_db():
    # 注意：必须导入模型以注册元数据
    from ..models.item import Materials  # 导入模型类
    Base.metadata.create_all(bind=engine)  # 创建所有表

# 函数：创建所有表 (如果不存在)
# 在应用启动时调用此函数可能更合适
def create_db_and_tables():
    try:
        Base.metadata.create_all(bind=engine)
        print("数据库表创建成功（如果不存在）。")
    except Exception as e:
        print(f"创建数据库表时出错: {e}")
