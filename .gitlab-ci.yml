stages:
  - build

variables:
  DOCKER_REGISTRY: "harbor.shecc.com"
  PROJECT_PATH: "tools/materials"
  IMAGE_NAME: "$DOCKER_REGISTRY/$PROJECT_PATH"

build-and-push:
  stage: build
  image: 
    name: harbor.shecc.com/tools/kaniko-excutor:debug # Use Kaniko executor image
    entrypoint: [""]
  before_script:   
    - AUTH_STRING=$(echo -n "$CI_REGISTRY_USER:$CI_REGISTRY_PASSWORD" | base64)
    - echo "{\"auths\":{\"$DOCKER_REGISTRY\":{\"auth\":\"$AUTH_STRING\"}}}" > /kaniko/.docker/config.json
  script:
    - echo "Preparing to build and push Docker image..."
    # Build and push the image using Kaniko
    - /kaniko/executor --context . 
                       --dockerfile Dockerfile 
                       --destination $IMAGE_NAME:$CI_JOB_ID 
                       --cache=true 
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /deploy/'   #提交信息中包含“关键字”时触发
