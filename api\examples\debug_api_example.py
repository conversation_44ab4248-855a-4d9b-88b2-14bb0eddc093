# API调试示例脚本

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from api.core.debug import enable_api_debug
from api.core.api_client import APIClient
from api.config import EMBEDDING_MODEL, CHAT_MODEL


async def test_api_debugging():
    """测试API调试功能"""
    print("\n===== API调试示例 =====")
    
    # 启用API调试模式
    print("\n1. 启用API调试模式")
    enable_api_debug(True)
    
    # 测试嵌入向量生成
    print("\n2. 测试嵌入向量生成")
    text = "这是一个测试文本，用于生成嵌入向量。"
    embedding = APIClient.generate_embedding(text, EMBEDDING_MODEL)
    print(f"生成的嵌入向量维度: {len(embedding)}")
    
    # 测试信息提取
    print("\n3. 测试信息提取")
    text = "我想了解一下华为P50手机，大概5000元左右的那款。"
    info = await APIClient.extract_info(text)
    print(f"提取的信息: {info}")
    
    # 测试聊天响应
    print("\n4. 测试聊天响应")
    messages = [
        {"role": "system", "content": "你是一个有用的助手。"},
        {"role": "user", "content": "简单介绍一下Python语言的特点。"}
    ]
    
    print("开始接收流式响应:")
    response_text = ""
    async for chunk in APIClient.stream_chat_response(messages, CHAT_MODEL, 0.7):
        # 这里只打印前10个字符，避免输出过多
        print(f"收到响应块: {chunk[:10]}...")
        response_text += chunk
    
    # 禁用API调试模式
    print("\n5. 禁用API调试模式")
    enable_api_debug(False)
    
    print("\n===== API调试示例结束 =====")


if __name__ == "__main__":
    # 运行异步测试函数
    asyncio.run(test_api_debugging())