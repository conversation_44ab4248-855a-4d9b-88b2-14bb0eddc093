import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, List, Avatar, Card, Spin, message } from 'antd';
import { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown'; // 导入 Markdown 组件
import remarkGfm from 'remark-gfm'; // 导入 GFM 插件
import './style.less'; // 导入样式文件

interface Message {
  role: 'user' | 'assistant';
  content: string; // Full target content from API
  displayedContent?: string; // Content currently displayed (for typewriter effect)
  loading?: boolean;
}

const TYPEWRITER_SPEED = 30; // Milliseconds per character (Adjust speed as needed)

const ChatPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const typewriterIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const userScrolledRef = useRef<boolean>(false);

  // 滚动到最新消息
  const scrollToBottom = () => {
    if (!userScrolledRef.current) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // 处理滚动事件
  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      // 如果用户向上滚动，标记为用户已滚动
      if (scrollHeight - scrollTop - clientHeight > 30) {
        userScrolledRef.current = true;
      } else {
        // 如果滚动到底部附近，重置标记
        userScrolledRef.current = false;
      }
    }
  };

  // Effect for scrolling
  useEffect(() => {
    scrollToBottom();
    
    // 添加滚动事件监听
    const messagesContainer = messagesContainerRef.current;
    if (messagesContainer) {
      messagesContainer.addEventListener('scroll', handleScroll);
    }
    
    return () => {
      // 清理滚动事件监听
      if (messagesContainer) {
        messagesContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [messages]); // Scroll whenever messages change

  // Effect for Typewriter
  useEffect(() => {
    // Clear any existing interval when messages change
    if (typewriterIntervalRef.current) {
      clearInterval(typewriterIntervalRef.current);
      typewriterIntervalRef.current = null;
    }

    const lastMessage = messages[messages.length - 1];

    // Check if the last message is from the assistant and needs typing
    if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.loading) {
      const targetContent = lastMessage.content;
      const currentDisplayed = lastMessage.displayedContent || '';

      if (currentDisplayed.length < targetContent.length) {
        typewriterIntervalRef.current = setInterval(() => {
          setMessages(prevMessages => {
            const updatedMessages = [...prevMessages];
            const lastMsgIndex = updatedMessages.length - 1;
            const currentMsg = updatedMessages[lastMsgIndex];
            
            // Double check it's still the same message and needs typing
            if (currentMsg && currentMsg.role === 'assistant' && !currentMsg.loading) {
               const currentLength = currentMsg.displayedContent?.length || 0;
               const targetLength = currentMsg.content.length;

               if (currentLength < targetLength) {
                 updatedMessages[lastMsgIndex] = {
                   ...currentMsg,
                   displayedContent: currentMsg.content.substring(0, currentLength + 1),
                 };
                 
                 // 在打字机效果运行时，仅当用户没有手动滚动时才自动滚动
                 if (!userScrolledRef.current) {
                   // 使用requestAnimationFrame确保DOM更新后再滚动
                   requestAnimationFrame(() => {
                     scrollToBottom();
                   });
                 }
                 
                 return updatedMessages;
               } else {
                 // Typing finished for this message, clear interval
                 if (typewriterIntervalRef.current) {
                   clearInterval(typewriterIntervalRef.current);
                   typewriterIntervalRef.current = null;
                 }
               }
            } else {
               // State changed unexpectedly, clear interval
               if (typewriterIntervalRef.current) {
                 clearInterval(typewriterIntervalRef.current);
                 typewriterIntervalRef.current = null;
               }
            }
            return prevMessages; // Return previous state if no update needed or state changed
          });
        }, TYPEWRITER_SPEED);
      }
    }

    // Cleanup function to clear interval on unmount or before next effect run
    return () => {
      if (typewriterIntervalRef.current) {
        clearInterval(typewriterIntervalRef.current);
        typewriterIntervalRef.current = null;
      }
    };
  }, [messages]); // Re-run effect when messages array changes


  // 发送消息
  const sendMessage = async () => {
    if (!input.trim()) return;

    // Clear previous typewriter interval if any
     if (typewriterIntervalRef.current) {
        clearInterval(typewriterIntervalRef.current);
        typewriterIntervalRef.current = null;
     }

    // 添加用户消息
    const userMessage: Message = { role: 'user', content: input };
    // 添加一个空的助手消息，初始化 content 和 displayedContent
    const assistantMessage: Message = { role: 'assistant', content: '', displayedContent: '', loading: true };
    
    // Use functional update to ensure we have the latest state
    setMessages(prevMessages => [...prevMessages, userMessage, assistantMessage]);
    setInput('');
    setLoading(true);

    try {
      // Prepare message history for API (using the full content)
      const apiMessages = messages.concat(userMessage).map(({ role, content }) => ({
        role,
        content,
      }));

      let fullResponse = '';
      
      // 使用fetch API发送POST请求
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: apiMessages,
          model: 'llama3', // 默认模型
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;

        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.substring(6);
              
              if (data === '[DONE]') {
                 // Stream finished, mark loading as false for the last message
                 setMessages(prev => {
                    const updated = [...prev];
                    const lastMsgIndex = updated.length - 1;
                    if (updated[lastMsgIndex]?.role === 'assistant') {
                       updated[lastMsgIndex] = { ...updated[lastMsgIndex], loading: false };
                    }
                    return updated;
                 });
                 // Ensure typewriter catches up if stream ends quickly
                 // The useEffect hook will handle the final display update
                break; 
              }
              
              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  fullResponse += parsed.content;
                  // Update only the target content of the last message
                  setMessages(prev => {
                    const updated = [...prev];
                    const lastMsgIndex = updated.length - 1;
                    // Ensure the last message is still the assistant message we are updating
                    if (updated[lastMsgIndex]?.role === 'assistant') {
                       updated[lastMsgIndex] = {
                         ...updated[lastMsgIndex],
                         content: fullResponse, // Update target content
                         loading: true, // Keep loading true while streaming
                         // displayedContent is updated by the useEffect typewriter
                       };
                    }
                    return updated;
                  });
                }
                
                if (parsed.error) {
                  message.error(`错误: ${parsed.error}`);
                  // Handle error state - update last message content and stop loading
                  setMessages(prev => {
                      const updated = [...prev];
                      const lastMsgIndex = updated.length - 1;
                      if (updated[lastMsgIndex]?.role === 'assistant') {
                         updated[lastMsgIndex] = {
                           ...updated[lastMsgIndex],
                           content: `错误: ${parsed.error}`,
                           displayedContent: `错误: ${parsed.error}`, // Show error immediately
                           loading: false,
                         };
                      }
                      return updated;
                  });
                  done = true; // Stop processing stream on error
                  break;
                }
              } catch (e) {
                console.error('解析SSE数据失败:', e, 'Data:', data);
                 // Potentially update UI to show a generic parsing error
              }
            }
          }
        }
      }
       // Final check to ensure loading is false after stream ends, even if no [DONE] received explicitly
       setMessages(prev => {
          const updated = [...prev];
          const lastMsgIndex = updated.length - 1;
          if (updated[lastMsgIndex]?.role === 'assistant' && updated[lastMsgIndex]?.loading) {
             updated[lastMsgIndex] = { ...updated[lastMsgIndex], loading: false };
          }
          return updated;
       });

    } catch (error) {
      console.error('发送消息时出错:', error);
      message.error('发送消息失败，请稍后重试');
      
      // Update error state for the last message
      setMessages(prev => {
        const updated = [...prev];
        const lastMsgIndex = updated.length - 1;
         if (updated[lastMsgIndex]?.role === 'assistant') {
            updated[lastMsgIndex] = {
              role: 'assistant',
              content: '抱歉，我遇到了一些问题，无法回应您的消息。请稍后再试。',
              displayedContent: '抱歉，我遇到了一些问题，无法回应您的消息。请稍后再试。', // Show error immediately
              loading: false,
            };
         }
        return updated;
      });
    } finally {
      setLoading(false); // Input loading state
    }
  };

  return (
    <div className="chat-page-container"> 
      <Card title="智能助手" className="chat-card" bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}>
        <div className="messages-container" ref={messagesContainerRef}> 
          {messages.length === 0 ? (
            <div className="empty-chat">
              <p>开始对话吧！</p> 
            </div>
          ) : (
            <List
              itemLayout="horizontal"
              dataSource={messages}
              renderItem={(message, index) => ( // Added index for key
                <List.Item key={index} className={`message-item ${message.role}`}> 
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                      shape='circle'
                        src={    message.role === 'user' 
                          ? null:
                         "https://www.shecc.com:8443/static/logo/5.png" 
                        }
                        size="large"
                        icon={
                        message.role === 'user' 
                          ? <UserOutlined /> 
                          : <RobotOutlined />
                      } />
                    }
                    description={
                      <div className="message-content-wrapper"> 
                        {message.loading && message.role === 'assistant' && !message.displayedContent ? ( // Show spinner only if assistant is loading AND has no displayed content yet
                          <Spin size="small" />
                        ) : (
                          <div className="message-content">
                            {message.role === 'assistant' ? (
                              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                {message.displayedContent || ''} 
                              </ReactMarkdown>
                            ) : (
                              message.content // User message directly
                            )}
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item> 
              )}
            />
          )}
          <div ref={messagesEndRef} />
        </div>
        
        <div className="input-container"> 
          <Input.TextArea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="chat-input"
            placeholder="您可以向我询问关于材料的问题..."
            autoSize={{ minRows: 4, maxRows: 4 }}
            onPressEnter={(e) => {
              if (!e.shiftKey && !loading) { // Prevent sending while loading
                e.preventDefault();
                sendMessage();
              }
            }}
            disabled={loading}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={sendMessage}
            loading={loading}
            disabled={!input.trim() || loading} // Disable while loading
            className="send-button"
          >
            发送
          </Button> 
        </div>
      </Card>
    </div>
  );
};

export default ChatPage;
