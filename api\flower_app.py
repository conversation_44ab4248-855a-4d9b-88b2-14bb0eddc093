# -*- coding: utf-8 -*-
"""
Flower启动模块
用于启动Celery Flower监控界面
"""

import sys
import os
import logging
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("flower_app")

def start_flower(
    host: str = "localhost",
    port: int = 5555,
    debug: bool = True,
    basic_auth: Optional[str] = None,
    extra_args: Optional[List[str]] = None
) -> None:
    """
    启动Flower监控界面

    Args:
        host: 监听地址
        port: 监听端口
        debug: 是否启用调试模式
        basic_auth: 基本认证，格式为"username:password"
        extra_args: 额外的命令行参数
    """
    logger.info("启动Flower监控界面")

    # 从配置文件获取Redis连接信息
    from .config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB

    # 构建Broker URL
    broker_url = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    logger.info(f"使用Broker URL: {broker_url}")

    # 检查Flower版本
    import pkg_resources
    flower_version = pkg_resources.get_distribution("flower").version
    logger.info(f"检测到Flower版本: {flower_version}")

    # 根据版本构建命令行参数
    is_version_2_plus = flower_version.startswith("2.")

    if is_version_2_plus:
        # Flower 2.0+使用子命令格式
        logger.info("使用Flower 2.0+命令行格式")

        # 检查是否支持run子命令
        import subprocess
        help_output = subprocess.check_output([sys.executable, "-m", "flower", "--help"], text=True)
        supports_run = "run" in help_output

        if supports_run:
            # 支持run子命令
            logger.info("检测到支持run子命令")
            argv = [
                sys.argv[0],
                "--broker", broker_url
            ]

            # 添加run子命令
            argv.append("run")

            # 添加run子命令的选项
            argv.extend(["--port", str(port)])
            argv.extend(["--address", host])

            if debug:
                argv.append("--debug")

            # 添加基本认证
            if basic_auth:
                username, password = basic_auth.split(":", 1)
                argv.extend(["--basic-auth-username", username])
                argv.extend(["--basic-auth-password", password])
        else:
            # 不支持run子命令，使用传统格式
            logger.info("未检测到run子命令，使用传统格式")
            argv = [
                sys.argv[0],
                f"--broker={broker_url}",
                f"--port={port}",
                f"--address={host}",
                f"--debug={'True' if debug else 'False'}",
                "--persistent=True",
                "--db=flower.db",
                "--max_tasks=10000",
                "--tasks_columns=name,uuid,state,args,kwargs,result,received,started,runtime,worker"
            ]

            # 添加日志级别
            if debug:
                argv.append("--logging=DEBUG")
            else:
                argv.append("--logging=INFO")

            # 添加基本认证
            if basic_auth:
                argv.append(f"--basic_auth={basic_auth}")
    else:
        # Flower 1.x使用传统格式
        logger.info("使用Flower 1.x命令行格式")
        argv = [
            sys.argv[0],
            f"--broker={broker_url}",
            f"--port={port}",
            f"--address={host}",
            f"--debug={'True' if debug else 'False'}",
            "--persistent=True",
            "--db=flower.db",
            "--max_tasks=10000",
            "--tasks_columns=name,uuid,state,args,kwargs,result,received,started,runtime,worker"
        ]

        # 添加日志级别
        if debug:
            argv.append("--logging=DEBUG")
        else:
            argv.append("--logging=INFO")

        # 添加基本认证
        if basic_auth:
            argv.append(f"--basic_auth={basic_auth}")

    # 添加额外参数
    if extra_args:
        argv.extend(extra_args)

    # 隐藏密码
    safe_argv = []
    for i, arg in enumerate(argv):
        if i > 0 and argv[i-1] in ["--broker", "--basic-auth-password"]:
            if "--password" in arg:
                safe_argv.append("***")
            else:
                safe_argv.append("***")
        else:
            safe_argv.append(arg)

    logger.info(f"Flower命令行参数: {' '.join(safe_argv)}")

    # 启动Flower
    try:
        # 尝试导入Flower命令行接口
        logger.info("尝试导入Flower模块...")

        # 检查Flower是否已安装
        try:
            import pkg_resources
            flower_version = pkg_resources.get_distribution("flower").version
            logger.info(f"检测到Flower版本: {flower_version}")
        except pkg_resources.DistributionNotFound:
            logger.error("未检测到Flower包，尝试安装...")
            import subprocess
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "flower"])
                logger.info("Flower安装成功")
            except subprocess.CalledProcessError as e:
                logger.error(f"安装Flower失败: {e}")
                sys.exit(1)

        # 显示Python路径
        logger.info(f"Python路径: {sys.executable}")
        logger.info(f"sys.path: {sys.path}")

        # 尝试直接使用subprocess启动Flower
        logger.info("尝试使用subprocess启动Flower...")
        import subprocess

        # 构建命令
        cmd = [sys.executable, "-m", "flower"]
        for arg in argv[1:]:  # 跳过第一个参数（脚本名称）
            cmd.append(arg)

        logger.info(f"执行命令: {' '.join(cmd)}")

        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 使用线程读取输出，避免阻塞
        import threading

        def read_output(stream, log_func, prefix):
            for line in iter(stream.readline, ''):
                if line:
                    log_func(f"[{prefix}] {line.strip()}")

        # 创建并启动线程
        stdout_thread = threading.Thread(
            target=read_output,
            args=(process.stdout, logger.info, "Flower"),
            daemon=True
        )
        stderr_thread = threading.Thread(
            target=read_output,
            args=(process.stderr, logger.error, "Flower Error"),
            daemon=True
        )

        stdout_thread.start()
        stderr_thread.start()

        # 输出启动信息
        logger.info("Flower已启动，请访问: http://localhost:5555")

        # 保持进程运行
        try:
            process.wait()
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在终止Flower进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning("Flower进程未能在5秒内终止，强制结束...")
                process.kill()

        if process.returncode != 0:
            logger.error(f"Flower进程退出，退出码: {process.returncode}")
            sys.exit(process.returncode)

    except ImportError as e:
        logger.error(f"导入错误: {e}")
        logger.error("未安装Flower，请先安装: pip install flower")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动Flower时出错: {e}", exc_info=True)
        sys.exit(1)

def main() -> None:
    """
    命令行入口点
    """
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动Celery Flower监控界面")
    parser.add_argument("--host", default="localhost", help="监听地址")
    parser.add_argument("--port", type=int, default=5555, help="监听端口")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--basic-auth", help="基本认证，格式为username:password")

    args = parser.parse_args()

    # 启动Flower
    start_flower(
        host=args.host,
        port=args.port,
        debug=args.debug,
        basic_auth=args.basic_auth
    )

if __name__ == "__main__":
    main()
