# 任务管理页面集成测试指南

## 测试目标
验证任务管理页面已成功集成Excel材料处理任务的管理功能，实现统一的任务管理界面。

## 测试环境准备

### 1. 启动完整服务
```bash
# 启动后端API
uvicorn api.main:app --reload

# 启动RQ Worker（新终端）
python start_rq_worker.py

# 启动RQ Dashboard（可选）
python start_rq_dashboard.py

# 启动前端服务（新终端）
cd web
npm start
```

### 2. 访问应用
- 前端界面：http://localhost:3000
- 任务管理页面：http://localhost:3000/tasks
- Excel材料处理页面：http://localhost:3000/excel-material

## 功能测试清单

### 1. 页面布局测试
- [ ] 任务管理页面正常加载
- [ ] 显示3个标签页：
  - [ ] "RQ活跃任务"标签页
  - [ ] "RQ已完成任务"标签页  
  - [ ] "Excel材料处理"标签页
- [ ] 标签页图标和徽章显示正确
- [ ] 页面布局美观，无样式错误

### 2. RQ任务管理测试
#### 2.1 RQ活跃任务标签页
- [ ] 切换到"RQ活跃任务"标签页
- [ ] 显示当前运行中的RQ任务
- [ ] 任务状态标签显示正确（等待中、执行中等）
- [ ] "新建任务"按钮正常工作
- [ ] "刷新"按钮正常工作
- [ ] "RQ Dashboard"按钮能打开监控界面
- [ ] 任务列表自动刷新（每10秒）

#### 2.2 RQ已完成任务标签页
- [ ] 切换到"RQ已完成任务"标签页
- [ ] 显示已完成的RQ任务历史
- [ ] 任务状态标签显示正确（成功、失败等）
- [ ] 分页功能正常工作
- [ ] "刷新"按钮正常工作

#### 2.3 RQ任务操作测试
- [ ] 点击"详情"按钮显示任务详情模态框
- [ ] 任务详情显示完整信息（ID、状态、结果等）
- [ ] 对活跃任务可以执行"取消"操作
- [ ] 取消任务后状态正确更新

### 3. Excel材料处理任务管理测试
#### 3.1 Excel任务标签页基础功能
- [ ] 切换到"Excel材料处理"标签页
- [ ] 徽章显示活跃任务数量（submitted、processing、ready_for_merge状态）
- [ ] 如果没有任务，显示提示信息和"前往Excel处理页面"按钮
- [ ] "刷新"按钮正常工作
- [ ] 任务列表自动刷新（每10秒）

#### 3.2 Excel任务列表显示
创建一些Excel处理任务后测试：
- [ ] 任务列表正确显示所有Excel处理任务
- [ ] 表格列显示完整：
  - [ ] 任务ID（可复制，显示前16位）
  - [ ] 文件名
  - [ ] 状态标签（颜色和图标正确）
  - [ ] 进度条（百分比和状态正确）
  - [ ] 数据行数
  - [ ] 创建时间（格式正确）
  - [ ] 操作按钮
- [ ] 分页功能正常（显示总数、快速跳转等）

#### 3.3 Excel任务操作测试
对于不同状态的Excel任务，测试相应操作：

**处理中的任务（submitted、processing）**
- [ ] "查看详情"按钮显示Excel任务详情模态框
- [ ] "删除任务"按钮能删除任务

**待合并的任务（ready_for_merge）**
- [ ] "查看详情"按钮正常工作
- [ ] "合并结果"按钮能触发合并操作
- [ ] "删除任务"按钮正常工作

**已完成的任务（completed）**
- [ ] "查看详情"按钮正常工作
- [ ] "下载结果"按钮能下载Excel文件
- [ ] "删除任务"按钮正常工作

#### 3.4 Excel任务详情模态框测试
- [ ] 点击"查看详情"打开专用的Excel任务详情模态框
- [ ] 显示批次统计信息：
  - [ ] 总批次数
  - [ ] 已完成批次数
  - [ ] 失败批次数
- [ ] 显示任务基本信息：
  - [ ] 任务ID（可复制）
  - [ ] 状态标签
  - [ ] 文件名
  - [ ] 数据行数
  - [ ] 创建时间
  - [ ] 过期时间
- [ ] 如果有处理统计，显示详细统计：
  - [ ] 总处理数
  - [ ] 成功数
  - [ ] 失败数
  - [ ] 价格更新数
- [ ] 模态框样式美观，信息布局合理

### 4. 任务状态同步测试
#### 4.1 状态标签一致性
- [ ] Excel任务状态标签与Excel处理页面一致
- [ ] RQ任务状态标签显示正确
- [ ] 状态颜色和图标符合设计规范

#### 4.2 实时更新测试
- [ ] 在Excel处理页面创建新任务
- [ ] 任务管理页面能自动显示新任务
- [ ] 任务状态变化时，两个页面同步更新
- [ ] 任务完成后，状态正确更新

### 5. 页面交互测试
#### 5.1 页面跳转
- [ ] 从任务管理页面点击"前往Excel处理页面"按钮
- [ ] 能正确跳转到Excel材料处理页面
- [ ] 在新标签页中打开，不影响当前页面

#### 5.2 操作反馈
- [ ] 所有操作都有适当的加载状态
- [ ] 成功操作显示成功消息
- [ ] 错误操作显示错误消息
- [ ] 消息提示内容准确、友好

### 6. 数据一致性测试
#### 6.1 任务数据同步
- [ ] 任务管理页面显示的任务数据与后端API一致
- [ ] Excel任务的详细信息与Excel处理页面一致
- [ ] 任务操作结果在两个页面都能正确反映

#### 6.2 状态转换测试
创建一个完整的Excel处理任务，观察状态转换：
- [ ] submitted → processing
- [ ] processing → ready_for_merge
- [ ] ready_for_merge → completed（合并后）
- [ ] 每个状态转换都在任务管理页面正确显示

### 7. 错误处理测试
#### 7.1 网络错误
- [ ] 断开网络连接时显示适当错误信息
- [ ] 网络恢复后能正常加载数据
- [ ] API错误时显示具体错误信息

#### 7.2 操作错误
- [ ] 对不存在的任务执行操作时显示错误
- [ ] 权限不足时显示相应提示
- [ ] 操作失败时不影响页面其他功能

### 8. 性能测试
#### 8.1 加载性能
- [ ] 页面首次加载时间 < 3秒
- [ ] 任务列表加载时间 < 2秒
- [ ] 切换标签页响应时间 < 1秒

#### 8.2 大数据量测试
创建多个任务（建议20+）测试：
- [ ] 大量任务时页面加载正常
- [ ] 分页功能正常工作
- [ ] 自动刷新不影响性能
- [ ] 内存使用合理，无内存泄漏

### 9. 用户体验测试
#### 9.1 界面友好性
- [ ] 界面布局清晰，信息层次分明
- [ ] 操作按钮位置合理，易于点击
- [ ] 状态标签颜色区分明显
- [ ] 加载状态显示清楚

#### 9.2 操作便利性
- [ ] 常用操作容易找到和执行
- [ ] 任务ID复制功能方便使用
- [ ] 批量操作（如果有）效率高
- [ ] 快捷键支持（如果有）

### 10. 浏览器兼容性测试
测试以下浏览器：
- [ ] Chrome（最新版本）
- [ ] Firefox（最新版本）
- [ ] Safari（最新版本）
- [ ] Edge（最新版本）

## 测试数据准备

### 1. 创建测试任务
```bash
# 创建RQ任务
curl -X POST "http://localhost:8000/tasks/run" \
  -H "Content-Type: application/json" \
  -d '{"task_type": "sync_materials"}'

# 创建Excel处理任务（通过前端上传文件）
# 或使用API直接创建
```

### 2. 测试Excel文件
使用之前创建的测试Excel文件，包含：
- 名称、型号、规格、数量、价格、备注等列
- 至少10行数据
- 确保有些数据能匹配到价格

## 预期结果

### 成功场景
1. **统一管理界面**：一个页面管理所有类型的任务
2. **状态同步**：任务状态在不同页面间保持一致
3. **操作完整**：支持查看、操作、删除等完整功能
4. **实时更新**：任务状态自动刷新，无需手动刷新
5. **用户友好**：界面清晰，操作简单，反馈及时

### 错误场景处理
1. **网络错误**：显示友好错误信息，支持重试
2. **API错误**：显示具体错误详情
3. **权限错误**：显示权限不足提示
4. **数据错误**：显示数据格式错误信息

## 问题记录模板

### 问题1
- **描述**：
- **重现步骤**：
- **预期结果**：
- **实际结果**：
- **严重程度**：高/中/低
- **状态**：待修复/已修复/已验证

### 问题2
- **描述**：
- **重现步骤**：
- **预期结果**：
- **实际结果**：
- **严重程度**：高/中/低
- **状态**：待修复/已修复/已验证

## 测试完成确认

- [ ] 所有功能测试项目已完成
- [ ] 所有发现的问题已记录
- [ ] 关键功能正常工作
- [ ] 用户体验满足要求
- [ ] 性能指标达标
- [ ] 浏览器兼容性良好
- [ ] 数据一致性验证通过

**测试人员**：_____________  
**测试日期**：_____________  
**测试版本**：_____________  
**测试结果**：□ 通过 □ 不通过  

## 备注
其他需要说明的问题或建议：
