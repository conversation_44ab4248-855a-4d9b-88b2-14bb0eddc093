from pydantic import BaseModel
from typing import Optional

# 基础 Schema，包含所有共享字段
class MaterialsBase(BaseModel):
    serial_number: Optional[int] = None # 允许从 Excel 读取时为空或非整数
    product_name: Optional[str] = None
    contract_id: Optional[str] = None
    contract_number: Optional[str] = None
    specification: Optional[str] = None
    brand: Optional[str] = None
    origin: Optional[str] = None
    quantity: Optional[float] = None
    unit: Optional[str] = None
    unit_price: Optional[float] = None
    amount: Optional[float] = None
    remarks: Optional[str] = None
    warranty_months: Optional[int] = None
    includes_delivery_installation: Optional[str] = None

    # Pydantic V2 配置
    class Config:
        from_attributes = True # 允许从 ORM 模型属性创建 Schema 实例 (替代 orm_mode)

# 用于创建记录的 Schema (继承自 Base)
# 通常不需要 id，因为它是自动生成的
class MaterialsCreate(MaterialsBase):
    pass # 继承所有字段，目前没有额外需要

# 用于从数据库读取记录的 Schema (包含 id)
class Materials(MaterialsBase):
    id: int
