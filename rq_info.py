#!/usr/bin/env python
"""
RQ信息查看脚本
用于查看RQ队列和Worker状态
"""

import sys
import redis
from rq import Queue
from rq.job import Job
from rq.worker import Worker

# Redis连接URL
REDIS_URL = 'redis://:yhb25IEz@10.100.4.17:6379/3'

def get_redis_connection():
    """获取Redis连接"""
    return redis.from_url(REDIS_URL)

def show_queues():
    """显示所有队列信息"""
    conn = get_redis_connection()

    # 获取所有队列
    queues = ['default', 'high', 'low']

    print("\n===== 队列信息 =====")
    for queue_name in queues:
        queue = Queue(queue_name, connection=conn)
        job_count = len(queue)
        print(f"队列: {queue_name}")
        print(f"  - 任务数量: {job_count}")

        # 显示队列中的任务
        if job_count > 0:
            print("  - 任务列表:")
            for job_id in queue.job_ids:
                job = Job.fetch(job_id, connection=conn)
                print(f"    * 任务ID: {job.id}")
                print(f"      创建时间: {job.created_at}")
                print(f"      状态: {job.get_status()}")
                print(f"      函数: {job.func_name}")
                print()

def show_workers():
    """显示所有Worker信息"""
    conn = get_redis_connection()

    # 获取所有Worker
    workers = Worker.all(connection=conn)

    print("\n===== Worker信息 =====")
    print(f"Worker数量: {len(workers)}")

    for i, worker in enumerate(workers):
        print(f"Worker {i+1}:")
        print(f"  - ID: {worker.key}")
        print(f"  - 名称: {worker.name}")
        print(f"  - 状态: {worker.get_state()}")
        print(f"  - 生命周期: {worker.birth_date}")
        print(f"  - 监听队列: {', '.join(q.name for q in worker.queues)}")

        # 显示当前任务
        current_job = worker.get_current_job()
        if current_job:
            print(f"  - 当前任务: {current_job.id} ({current_job.func_name})")
        else:
            print("  - 当前任务: 无")
        print()

def show_failed_jobs():
    """显示失败的任务"""
    conn = get_redis_connection()

    # 获取失败队列
    failed_queue = Queue('failed', connection=conn)

    print("\n===== 失败任务 =====")
    print(f"失败任务数量: {len(failed_queue)}")

    if len(failed_queue) > 0:
        for job_id in failed_queue.job_ids:
            job = Job.fetch(job_id, connection=conn)
            print(f"任务ID: {job.id}")
            print(f"  - 创建时间: {job.created_at}")
            print(f"  - 函数: {job.func_name}")
            print(f"  - 异常信息: {job.exc_info}")
            print()

if __name__ == "__main__":
    print("RQ信息查看工具")
    print(f"Redis URL: {REDIS_URL}")

    show_queues()
    show_workers()
    show_failed_jobs()

    print("\n提示: 如果无法通过RQ Dashboard查看任务，可以使用此脚本查看任务状态。")
