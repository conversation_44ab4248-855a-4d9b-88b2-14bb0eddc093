{"version": "2.0.0", "tasks": [{"label": "启动Web前端", "type": "npm", "script": "dev", "path": "web/", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "启动Web前端开发服务器"}, {"label": "启动API服务", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["-m", "u<PERSON><PERSON>", "api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "启动FastAPI服务"}, {"label": "启动RQ Worker", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["-m", "rq", "worker", "--url", "redis://***********:6379/3", "--name", "worker@%h", "default", "high", "low"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "启动RQ Worker", "options": {"env": {"PYTHONPATH": "${workspaceFolder}"}}}, {"label": "启动RQ Scheduler", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["-m", "rq_scheduler.scheduler", "--url", "redis://***********:6379/3", "--interval", "60"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "启动RQ Scheduler", "options": {"env": {"PYTHONPATH": "${workspaceFolder}"}}}, {"label": "启动RQ Dashboard", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["-m", "rq_dashboard", "--redis-url", "redis://***********:6379/3", "--port", "9181"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "启动RQ Dashboard监控界面"}, {"label": "测试Flower", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["test_flower.py"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "测试Flower是否可以正常启动"}, {"label": "调试启动Flower", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["flower_debug.py"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "在调试模式下启动Flower", "options": {"env": {"DEBUG": "True", "LOG_LEVEL": "DEBUG", "PYTHONPATH": "${workspaceFolder}"}}}, {"label": "简单启动Flower", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["simple_flower.py"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "使用简单配置启动Flower"}, {"label": "直接启动Flower", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["direct_flower.py"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "直接启动Flower，不使用临时文件"}, {"label": "检查Flower依赖项", "type": "shell", "command": "${command:python.interpreterPath}", "args": ["check_flower_deps.py"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new", "focus": true}, "detail": "检查Flower依赖项"}, {"label": "启动API + Worker", "dependsOn": ["启动API服务", "启动RQ Worker"], "dependsOrder": "parallel", "problemMatcher": [], "detail": "并行启动API服务和RQ Worker"}, {"label": "启动API + Worker + Scheduler", "dependsOn": ["启动API服务", "启动RQ Worker", "启动RQ Scheduler"], "dependsOrder": "parallel", "problemMatcher": [], "detail": "并行启动API服务、RQ Worker和Scheduler"}, {"label": "启动API + Worker + Dashboard", "dependsOn": ["启动API服务", "启动RQ Worker", "启动RQ Dashboard"], "dependsOrder": "parallel", "problemMatcher": [], "detail": "并行启动API服务、RQ Worker和Dashboard"}, {"label": "启动API + Worker + Scheduler + Dashboard", "dependsOn": ["启动API服务", "启动RQ Worker", "启动RQ Scheduler", "启动RQ Dashboard"], "dependsOrder": "parallel", "problemMatcher": [], "detail": "并行启动API服务、RQ Worker、Scheduler和Dashboard"}, {"label": "启动Worker + Scheduler + Dashboard", "dependsOn": ["启动RQ Worker", "启动RQ Scheduler", "启动RQ Dashboard"], "dependsOrder": "parallel", "problemMatcher": [], "detail": "并行启动RQ Worker、Scheduler和Dashboard"}, {"label": "启动所有服务", "dependsOn": ["启动API服务", "启动RQ Worker", "启动RQ Scheduler", "启动RQ Dashboard", "启动Web前端"], "dependsOrder": "parallel", "problemMatcher": [], "detail": "并行启动所有服务"}]}