# Milvus向量数据库操作模块

from typing import List, Dict, Any, Optional, Union

from FlagEmbedding.inference import reranker
from ..config import MILVUS_COLLECTION_NAME
from ..core.logging_config import get_logger
from .vector_embedding import VectorEmbedding

logger = get_logger("vector_db_milvus")

# 定义输出字段常量
MILVUS_OUTPUT_FIELDS = ["code", "name", "unit_price", "brand", "origin", "specification", "contract_id", "contract_number", "warranty_months", "includes_delivery_installation"]

class MilvusClient:
    """Milvus数据库操作类"""

    def __init__(self, collection, primary_field_name: str, dense_vector_field: str, sparse_vector_field: str):
        self.collection = collection
        self.primary_field_name = primary_field_name
        self.dense_vector_field = dense_vector_field
        self.sparse_vector_field = sparse_vector_field
        try:
            from pymilvus import (
                connections, Collection, utility,
                FieldSchema, CollectionSchema, DataType, AnnSearchRequest, RRFRanker,
                WeightedRanker, Function, FunctionType
            )
            self.milvus_modules = {
                "connections": connections,
                "Collection": Collection,
                "utility": utility,
                "FieldSchema": FieldSchema,
                "CollectionSchema": CollectionSchema,
                "DataType": DataType,
                "AnnSearchRequest": AnnSearchRequest,
                "RRFRanker": RRFRanker,
                "WeightedRanker": WeightedRanker,
                "Function": Function,
                "FunctionType": FunctionType
            }
            # 连接到Milvus
           

        except ImportError:
            logger.error("未安装pymilvus库，请使用pip install pymilvus安装")
            raise
        except Exception as e:
            logger.error(f"连接Milvus时出错: {e}")
            raise


    async def upsert_item(self, item_data: dict) -> bool:
        """向Milvus中插入或更新一条记录"""
        try:
            item_id = item_data.get("id")
            if item_id is None:
                logger.error("Upsert Milvus 失败：缺少 'id' 字段")
                return False

            # 准备用于生成嵌入的文本
            text_parts = []
            for key, value in item_data.items():
                if key in ['product_name', 'specification', 'brand'] and value is not None:
                    text_parts.append(f"{str(value).strip()}")
            combined_text = ' '.join(text_parts)
            item_data['search_text'] = combined_text

            if not combined_text:
                logger.warning(f"ID {item_id} 的可嵌入文本为空，跳过插入")
                return False

            # 生成混合嵌入向量
            embedding_result = await VectorEmbedding.generate_hybrid_embedding(combined_text)
            if not embedding_result:
                logger.error(f"为 ID {item_id} 生成混合嵌入向量失败")
                return False

            # 准备数据
            def safe_float(value, default=0.0):
                try: return float(value) if value not in (None, "") else default
                except (ValueError, TypeError): return default
            def safe_str(value, default=""): return str(value) if value is not None else default
            def safe_int(value, default=0): # 添加 safe_int
                try: return int(value) if value not in (None, "") else default
                except (ValueError, TypeError): return default
            def safe_str(value, default=""): return str(value) if value is not None else default

            data_dict = {
                "code": [item_id],
                "name": [safe_str(item_data.get("product_name"))],
                "contract_id": [safe_str(item_data.get("contract_id"))],
                "contract_number": [safe_str(item_data.get("contract_number"))],
                "specification": [safe_str(item_data.get("specification"))],
                "brand": [safe_str(item_data.get("brand"))],
                "origin": [safe_str(item_data.get("origin"))],
                "quantity": [safe_float(item_data.get("quantity"))],
                "unit": [safe_str(item_data.get("unit"))],
                "unit_price": [safe_float(item_data.get("unit_price"))],
                "amount": [safe_float(item_data.get("amount"))],
                "search_text": [safe_str(item_data.get("search_text"))],
                "warranty_months": [safe_int(item_data.get("warranty_months"))], # 添加 warranty_months
                "includes_delivery_installation": [safe_str(item_data.get("includes_delivery_installation"))], # 添加 includes_delivery_installation
                self.dense_vector_field: embedding_result['dense'],
                self.sparse_vector_field: embedding_result['sparse']
            }

            # 转换成列表格式
            data = [data_dict[field.name] for field in self.collection.schema.fields if field.name != 'sparse_bm25']
            self.collection.insert(data)
            logger.debug(f"已向Milvus插入/更新ID为 {item_id} 的记录 (含混合向量)")
            return True

        except Exception as e:
            logger.error(f"向Milvus插入/更新记录 ID {item_id} 时出错: {e}")
            return False

    async def delete_item(self, item_id: int) -> bool:
        """从Milvus中删除一条记录"""
        try:
            self.collection.load()
            delete_expr = f"{self.primary_field_name} == {item_id}"
            self.collection.delete(delete_expr)
            logger.debug(f"已从Milvus删除ID为 {item_id} 的记录")
            return True
        except Exception as e:
            logger.error(f"从Milvus删除记录 ID {item_id} 时出错: {e}")
            return False

    async def search_items(self, query_text: str, limit: int = 10, threshold: float = None, sparse_weight: float = 0.5, dense_weight: float = 0.5) -> List[Dict[str, Any]]:
        """在Milvus中执行混合搜索
        
        Args:
            query_text: 查询文本
            limit: 返回结果数量限制
            threshold: 相似度阈值
            sparse_weight: 稀疏向量权重，默认0.5
            dense_weight: 稠密向量权重，默认0.5
        """
        try:
            # 生成查询向量
            embedding_result = await VectorEmbedding.generate_hybrid_embedding(query_text)
            if not embedding_result:
                logger.error("生成查询向量失败")
                return []

            self.collection.load()
            AnnSearchRequest = self.milvus_modules["AnnSearchRequest"]
            WeightedRanker = self.milvus_modules["WeightedRanker"]

            # 准备搜索请求
            # 1. 准备稠密向量搜索请求
            dense_search_params = {"metric_type": "COSINE", "params": {"nprobe": 16}} # 示例参数
            dense_req = AnnSearchRequest(
                data=[embedding_result['dense']],
                anns_field=self.dense_vector_field,
                param=dense_search_params,
                limit=limit * 2 # 获取更多结果以便 RRF 排序
            )
           

            full_search_params = {'drop_ratio_search': 0.2} # 稀疏通常用 IP
            sparse_req = AnnSearchRequest(
                    data=[query_text],
                    anns_field="sparse_bm25",
                    param=full_search_params,
                    limit=limit * 2 # 获取更多结果以便 RRF 排序
                )
            # 3. 执行混合搜索
            # 使用 RRF (Reciprocal Rank Fusion) 进行重排序
            rerank = WeightedRanker(sparse_weight, dense_weight)
 
            results = self.collection.hybrid_search(
                reqs=[dense_req, sparse_req],
                rerank=rerank,
                limit=limit, # 最终返回的结果数量
                output_fields=MILVUS_OUTPUT_FIELDS # 使用常量
            )


            if not results or not results[0]:
                return []

            # 处理结果
            search_results = []
            for hits in results[0]:
                score = hits.score
                if threshold is None or score >= threshold:
                    item = {
                        "id": hits.entity.get("code"),
                        "product_name": hits.entity.get("name"),
                        "unit_price": hits.entity.get("unit_price"),
                        "specification": hits.entity.get("specification"),
                        "brand": hits.entity.get("brand"),
                        "origin": hits.entity.get("origin"),
                        
                        "contract_id": hits.entity.get("contract_id"),
                        "contract_number": hits.entity.get("contract_number"),
                        "warranty_months": hits.entity.get("warranty_months"), # 添加 warranty_months
                        "includes_delivery_installation": hits.entity.get("includes_delivery_installation"), # 添加 includes_delivery_installation
                        "score": score
                    }
                    search_results.append(item)

            logger.info(f"Milvus混合搜索完成，找到{len(search_results)}条结果")
            return search_results

        except Exception as e:
            logger.error(f"执行Milvus混合搜索时出错: {e}")
            return []
