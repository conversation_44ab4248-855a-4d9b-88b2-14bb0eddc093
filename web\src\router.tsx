import React from 'react';
import { lazy } from 'react';
import { RouteObject, useRoutes } from 'react-router-dom';

const MainLayout = lazy(() => import('./layouts/MainLayout'));
const HomePage = lazy(() => import('./components/HomePage'));
const ImportPage = lazy(() => import('./components/ImportPage'));
const QueryPage = lazy(() => import('./components/QueryPage'));
const ChatPage = lazy(() => import('./components/ChatPage'));
const VectorSearchPage = lazy(() => import('./components/VectorSearchPage')); // 导入新页面
const PriceChatPage = lazy(() => import('./components/PriceChatPage'));
const SyncPage = lazy(() => import('./components/SyncPage'));
const TasksPage = lazy(() => import('./components/TasksPage')); // 导入任务管理页面
const ExcelMaterialPage = lazy(() => import('./components/ExcelMaterialPage')); // 导入Excel材料处理页面

export const routes: RouteObject[] = [
  {
    path: '/',
    element: <MainLayout />,
    children: [
      { index: true, element: <HomePage /> },
      { path: 'import', element: <ImportPage /> },
      { path: 'query', element: <QueryPage /> },
      { path: 'chat', element: <ChatPage /> },
      { path: 'vector-search', element: <VectorSearchPage /> }, // 添加新路由
      { path: 'price-chat', element: <PriceChatPage /> },
      { path: 'sync', element: <SyncPage /> },
      { path: 'tasks', element: <TasksPage /> }, // 添加任务管理路由
      { path: 'excel-material', element: <ExcelMaterialPage /> } // 添加Excel材料处理路由
    ]
  }
];

// 创建默认导出的RouteElement组件
export default function RouteElement() {
  return useRoutes(routes);
}
