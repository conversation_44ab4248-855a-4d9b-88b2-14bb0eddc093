# Excel材料处理系统使用指南

本系统提供了完整的Excel材料数据处理功能，支持分批异步处理、价格查询和数据库匹配。

## 🚀 功能特性

### 核心功能
- **Excel文件上传和解析** - 支持 .xlsx 和 .xls 格式
- **分批异步处理** - 大文件自动分批处理，避免超时
- **API价格查询** - 异步调用外部API获取最新价格
- **数据库材料匹配** - 从现有材料库中查找匹配项
- **智能价格计算** - 根据优先级确定最终价格
- **结果文件生成** - 自动生成处理结果Excel文件
- **30天数据保留** - 自动清理过期数据
- **实时状态监控** - 查看处理进度和状态

### 支持的Excel列格式
| 列名 | 是否必需 | 说明 |
|------|----------|------|
| 名称 | ✅ 必需 | 产品名称 |
| 型号 | 可选 | 产品型号 |
| 规格 | 可选 | 产品规格 |
| 数量 | ✅ 必需 | 产品数量 |
| 价格 | 可选 | 原始价格 |
| 备注 | 可选 | 备注信息 |
| 品牌 | 可选 | 产品品牌 |
| 产地 | 可选 | 产品产地 |
| 单位 | 可选 | 计量单位 |

## 📋 API 端点

### 基础信息
- `GET /api/excel-material/info` - 获取系统功能信息

### 文件处理
- `POST /api/excel-material/upload` - 上传Excel文件并启动处理
- `GET /api/excel-material/task/{task_id}/status` - 查询任务状态
- `POST /api/excel-material/task/{task_id}/merge` - 合并处理结果
- `GET /api/excel-material/task/{task_id}/download` - 下载结果文件

### 任务管理
- `GET /api/excel-material/tasks` - 获取任务列表
- `DELETE /api/excel-material/task/{task_id}` - 删除任务
- `POST /api/excel-material/cleanup` - 清理过期任务

## 🔧 使用步骤

### 1. 启动系统

```bash
# 启动主应用
uvicorn api.main:app --reload

# 启动RQ Worker（新终端）
python start_rq_worker.py

# 启动RQ Dashboard（可选，新终端）
python start_rq_dashboard.py
```

### 2. 准备Excel文件

确保Excel文件包含必需的列：
- 第一行为标题行
- 包含"名称"和"数量"列
- 数据格式正确

示例Excel结构：
```
名称        | 型号      | 规格        | 数量 | 价格  | 备注
电缆        | YJV-3*120 | 3芯120平方  | 100  | 85.5  | 阻燃电缆
开关        | DZ47-63   | 63A单极     | 50   | 12.8  | 小型断路器
```

### 3. 上传文件

```bash
curl -X POST "http://localhost:8000/api/excel-material/upload" \
  -F "file=@test_materials.xlsx" \
  -F 'request_data={"header_row": 0, "batch_size": 50, "retention_days": 30, "description": "测试材料处理"}'
```

响应示例：
```json
{
  "task_id": "excel_material_abc12345",
  "status": "submitted",
  "message": "Excel文件已上传，处理任务已启动",
  "file_info": {
    "filename": "test_materials.xlsx",
    "size": 12345,
    "total_rows": 10,
    "headers": ["名称", "型号", "规格", "数量", "价格", "备注"]
  },
  "processing_info": {
    "batch_size": 50,
    "estimated_batches": 1,
    "retention_days": 30
  }
}
```

### 4. 监控处理进度

```bash
curl "http://localhost:8000/api/excel-material/task/excel_material_abc12345/status"
```

响应示例：
```json
{
  "task_id": "excel_material_abc12345",
  "status": "processing",
  "progress": 75.0,
  "total_batches": 4,
  "completed_batches": 3,
  "failed_batches": 0,
  "created_at": "2025-05-27T10:00:00",
  "expires_at": "2025-06-26T10:00:00"
}
```

### 5. 合并结果

当所有批次完成后：
```bash
curl -X POST "http://localhost:8000/api/excel-material/task/excel_material_abc12345/merge"
```

### 6. 下载结果

```bash
curl -O "http://localhost:8000/api/excel-material/task/excel_material_abc12345/download"
```

## 📊 处理流程

### 1. 文件解析阶段
- 验证Excel文件格式
- 提取标题行和数据
- 验证必需列是否存在
- 生成字段映射

### 2. 分批处理阶段
- 将数据分成小批次（默认50行/批）
- 每批次独立处理，避免超时
- 并行执行多个批次任务

### 3. 数据处理阶段
对每行数据执行：
- **数据清理** - 处理空值、格式转换
- **API查询** - 调用外部API获取价格信息
- **数据库匹配** - 在材料库中查找匹配项
- **价格计算** - 根据优先级确定最终价格

### 4. 价格优先级
1. **API价格** - 最高优先级，来自外部API
2. **数据库价格** - 中等优先级，来自材料库
3. **原始价格** - 最低优先级，Excel中的原始数据

### 5. 结果合并阶段
- 收集所有批次结果
- 生成统计信息
- 创建Excel结果文件
- 保存最终结果

## 📈 结果文件格式

生成的Excel文件包含两个工作表：

### 处理结果工作表
| 列名 | 说明 |
|------|------|
| 行号 | 原始数据行号 |
| 产品名称 | 产品名称 |
| 规格型号 | 规格型号 |
| 品牌 | 品牌信息 |
| 数量 | 产品数量 |
| 原始价格 | Excel中的原始价格 |
| 最终价格 | 计算后的最终价格 |
| 最终金额 | 数量 × 最终价格 |
| 价格来源 | api/database/original |
| 价格已更新 | 是/否 |
| 备注 | 备注信息 |
| 处理时间 | 处理时间戳 |

### 统计信息工作表
- 任务ID
- 原始文件名
- 总行数
- 处理成功数
- 处理失败数
- 价格更新数
- 处理时间
- 过期时间

## 🛠️ 配置选项

### 上传参数
```json
{
  "header_row": 0,        // 标题行索引（0开始）
  "batch_size": 50,       // 每批处理行数
  "retention_days": 30,   // 数据保留天数
  "description": "描述"   // 任务描述
}
```

### 系统配置
在 `api/config.py` 中：
```python
UPLOAD_DIR = "uploads"                    # 上传目录
DEFAULT_RETENTION_DAYS = 30               # 默认保留天数
```

在 `ExcelMaterialProcessor` 中：
```python
self.batch_size = 50                      # 默认批次大小
self.api_delay = 0.1                      # API请求间隔
```

## 🔍 任务状态说明

| 状态 | 说明 |
|------|------|
| submitted | 任务已提交 |
| processing | 正在处理中 |
| ready_for_merge | 批次完成，等待合并 |
| partial_complete | 部分批次失败 |
| completed | 全部完成 |
| failed | 处理失败 |
| merge_failed | 合并失败 |

## 🚨 错误处理

### 常见错误
1. **文件格式错误** - 只支持 .xlsx 和 .xls 格式
2. **缺少必需列** - 必须包含"名称"和"数量"列
3. **数据格式错误** - 数量和价格必须是数值
4. **任务不存在** - 任务ID无效或已过期

### 错误响应示例
```json
{
  "detail": "Excel文件缺少必需的列: ['名称']。请确保包含：名称、型号、规格、数量、价格、备注"
}
```

## 📝 使用示例

### Python示例
```python
import requests

# 上传文件
with open('materials.xlsx', 'rb') as f:
    files = {'file': f}
    data = {
        'request_data': '{"header_row": 0, "batch_size": 50, "description": "材料价格更新"}'
    }
    response = requests.post('http://localhost:8000/api/excel-material/upload', 
                           files=files, data=data)
    task_info = response.json()
    task_id = task_info['task_id']

# 监控状态
import time
while True:
    status_response = requests.get(f'http://localhost:8000/api/excel-material/task/{task_id}/status')
    status = status_response.json()
    print(f"进度: {status['progress']:.1f}%")
    
    if status['status'] in ['ready_for_merge', 'completed']:
        break
    time.sleep(5)

# 合并结果
if status['status'] == 'ready_for_merge':
    merge_response = requests.post(f'http://localhost:8000/api/excel-material/task/{task_id}/merge')

# 下载结果
download_response = requests.get(f'http://localhost:8000/api/excel-material/task/{task_id}/download')
with open(f'result_{task_id}.xlsx', 'wb') as f:
    f.write(download_response.content)
```

## 🔧 维护操作

### 清理过期任务
```bash
curl -X POST "http://localhost:8000/api/excel-material/cleanup"
```

### 查看所有任务
```bash
curl "http://localhost:8000/api/excel-material/tasks?limit=100"
```

### 删除特定任务
```bash
curl -X DELETE "http://localhost:8000/api/excel-material/task/excel_material_abc12345"
```

## 📞 技术支持

如遇问题，请检查：
1. RQ Worker 是否正在运行
2. Redis 连接是否正常
3. 数据库连接是否正常
4. 文件权限是否正确

查看日志：
- 应用日志：控制台输出
- RQ Worker日志：Worker终端输出
- RQ Dashboard：http://localhost:9181

Excel材料处理系统现已完全配置并可正常使用！🎉
