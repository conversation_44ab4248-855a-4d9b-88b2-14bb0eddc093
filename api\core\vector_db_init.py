# 向量数据库初始化模块

from typing import Dict, Any
from ..config import (
    VECTOR_DB_TYPE,
    MILVUS_HOST, MILVUS_PORT, MILVUS_COLLECTION_NAME,
    WEAVIATE_URL, WEAVIATE_API_KEY, WEAVIATE_CLASS_NAME,
    USE_LOCAL_EMBEDDING
)
from ..core.logging_config import get_logger
from ..core import globals

logger = get_logger("vector_db_init")

class VectorDBInitializer:
    """向量数据库初始化类，负责Milvus和Weaviate的初始化"""

    @staticmethod
    def init_milvus() -> Dict[str, Any]:
        """初始化Milvus连接和模块"""
        try:
            from pymilvus import (
                connections, Collection, utility,
                FieldSchema, CollectionSchema, DataType, AnnSearchRequest, RRFRanker,
                WeightedRanker, Function, FunctionType
            )
            milvus_modules = {
                "connections": connections,
                "Collection": Collection,
                "utility": utility,
                "FieldSchema": FieldSchema,
                "CollectionSchema": CollectionSchema,
                "DataType": DataType,
                "AnnSearchRequest": AnnSearchRequest,
                "RRFRanker": RRFRanker,
                "WeightedRanker": WeightedRanker,
                "Function": Function,
                "FunctionType": FunctionType
            }
            # 连接到Milvus
            connections.connect(host=MILVUS_HOST, port=MILVUS_PORT, db_name='materials')
            logger.info(f"已连接到Milvus: {MILVUS_HOST}:{MILVUS_PORT}")
            return milvus_modules

        except ImportError:
            logger.error("未安装pymilvus库，请使用pip install pymilvus安装")
            raise
        except Exception as e:
            logger.error(f"连接Milvus时出错: {e}")
            raise

    @staticmethod
    def init_milvus_collection(milvus_modules: Dict[str, Any], dense_vector_field: str, sparse_vector_field: str) -> tuple:
        """初始化Milvus集合"""
        connections = milvus_modules["connections"]
        Collection = milvus_modules["Collection"]
        utility = milvus_modules["utility"]
        FieldSchema = milvus_modules["FieldSchema"]
        CollectionSchema = milvus_modules["CollectionSchema"]
        DataType = milvus_modules["DataType"]
        Function = milvus_modules["Function"]
        FunctionType = milvus_modules["FunctionType"]

        primary_field_name = "code"
        collection = None

        # 检查集合是否存在
        if utility.has_collection(MILVUS_COLLECTION_NAME):
            collection = Collection(MILVUS_COLLECTION_NAME)
            logger.info(f"已加载Milvus集合: {MILVUS_COLLECTION_NAME}")
            try:
                schema = collection.schema
                primary_field_name = next((field.name for field in schema.fields if field.is_primary), "code")
                if sparse_vector_field not in [field.name for field in schema.fields]:
                    logger.warning(f"当前Milvus集合 '{MILVUS_COLLECTION_NAME}' 缺少稀疏向量字段 '{sparse_vector_field}'。需要重建集合以支持混合搜索。")
                else:
                    logger.info(f"Milvus集合包含稀疏向量字段 '{sparse_vector_field}'")
                logger.info(f"Milvus集合主键字段名: {primary_field_name}")
            except Exception as e:
                logger.warning(f"获取集合schema信息失败: {e}，将使用默认主键字段名'code'")
            return collection, primary_field_name

        # 创建新集合
        dense_dimension = globals.embedding_dimension if USE_LOCAL_EMBEDDING and globals.embedding_dimension is not None else 1024
        if dense_dimension is None and USE_LOCAL_EMBEDDING:
            logger.error("无法获取全局嵌入维度，将使用默认值 1024。请检查模型加载过程。")
            dense_dimension = 1024

        logger.info(f"创建新的Milvus集合 '{MILVUS_COLLECTION_NAME}'，支持混合搜索")
        logger.info(f"稠密向量维度: {dense_dimension}")

        fields = [
            FieldSchema(name="code", dtype=DataType.INT64, is_primary=True),
            FieldSchema(name="name", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="contract_id", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="contract_number", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="specification", dtype=DataType.VARCHAR, max_length=255),
            FieldSchema(name="brand", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="origin", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="quantity", dtype=DataType.FLOAT),
            FieldSchema(name="unit", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="unit_price", dtype=DataType.FLOAT),
            FieldSchema(name="amount", dtype=DataType.FLOAT),
            FieldSchema(name="search_text", dtype=DataType.VARCHAR, max_length=1000, enable_analyzer=True),
            FieldSchema(name="warranty_months", dtype=DataType.INT32, description="质量保修月份"),
            FieldSchema(name="includes_delivery_installation", dtype=DataType.VARCHAR, max_length=50, description="是否包含送货安装"),
            FieldSchema(name=dense_vector_field, dtype=DataType.FLOAT_VECTOR, dim=dense_dimension, description="Dense Vector (e.g., BGE-M3)"),
            FieldSchema(name=sparse_vector_field, dtype=DataType.SPARSE_FLOAT_VECTOR, description="Sparse Vector (e.g., BGE-M3)"),
            FieldSchema(name="sparse_bm25", dtype=DataType.SPARSE_FLOAT_VECTOR, description="全文检索")
        ]

        schema = CollectionSchema(fields=fields, description="材料价格库向量集合 (混合搜索)")

        bm25_function = Function(
            name="text_bm25_emb",
            input_field_names=["search_text"],
            output_field_names=["sparse_bm25"],
            function_type=FunctionType.BM25
        )
        schema.add_function(bm25_function)

        collection = Collection(name=MILVUS_COLLECTION_NAME, schema=schema)
        logger.info(f"Milvus集合 '{MILVUS_COLLECTION_NAME}' Schema创建成功")

        # 创建索引
        dense_index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 1024}
        }
        collection.create_index(field_name=dense_vector_field, index_params=dense_index_params)
        logger.info(f"为字段 '{dense_vector_field}' 创建稠密向量索引成功")

        sparse_index_params = {
            "metric_type": "IP",
            "index_type": "SPARSE_INVERTED_INDEX",
            "params": {"drop_ratio_build": 0.2}
        }
        collection.create_index(field_name=sparse_vector_field, index_params=sparse_index_params)
        logger.info(f"为字段 '{sparse_vector_field}' 创建稀疏向量索引成功")

        collection.create_index("sparse_bm25", index_params={
            "index_name": "sparse_inverted_index_bm25",
            "index_type": "SPARSE_INVERTED_INDEX",
            "metric_type": "BM25",
            "params": {
                "inverted_index_algo": "DAAT_MAXSCORE",
                "bm25_k1": 1.2,
                "bm25_b": 0.75
            }
        })

        logger.info(f"已创建支持混合搜索的Milvus集合和索引: {MILVUS_COLLECTION_NAME}")
        return collection, primary_field_name

    @staticmethod
    def init_weaviate() -> Any:
        """初始化Weaviate连接"""
        try:
            import weaviate
            client = weaviate.Client(
                url=WEAVIATE_URL,
                auth_client_secret=weaviate.AuthApiKey(WEAVIATE_API_KEY) if WEAVIATE_API_KEY else None
            )
            logger.info(f"已连接到Weaviate: {WEAVIATE_URL}")
            return client
        except ImportError:
            logger.error("未安装weaviate-client库，请使用pip install weaviate-client安装")
            raise
        except Exception as e:
            logger.error(f"连接Weaviate时出错: {e}")
            raise

    @staticmethod
    def init_weaviate_class(client: Any) -> None:
        """初始化Weaviate类"""
        try:
            class_obj = client.schema.get(WEAVIATE_CLASS_NAME)
            if class_obj is not None:
                logger.info(f"已加载Weaviate类: {WEAVIATE_CLASS_NAME}")
                return

            class_obj = {
                "class": WEAVIATE_CLASS_NAME,
                "description": "材料价格库向量数据",
                "properties": [
                    {"name": "product_name", "dataType": ["text"], "description": "产品名称"},
                    {"name": "specification", "dataType": ["text"], "description": "规格型号"},
                    {"name": "brand", "dataType": ["text"], "description": "产地"},
                    {"name": "original_id", "dataType": ["int"], "description": "原始数据库ID"}
                ]
            }
            client.schema.create_class(class_obj)
            logger.info(f"已创建Weaviate类: {WEAVIATE_CLASS_NAME}")
        except Exception as e:
            logger.error(f"初始化Weaviate类时出错: {e}")
            raise
