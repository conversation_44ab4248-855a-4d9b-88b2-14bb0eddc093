# Excel材料处理前端功能测试指南

## 测试环境准备

### 1. 启动后端服务
```bash
# 启动主应用
uvicorn api.main:app --reload

# 启动RQ Worker（新终端）
python start_rq_worker.py

# 启动RQ Dashboard（可选）
python start_rq_dashboard.py
```

### 2. 启动前端服务
```bash
cd web
npm install  # 如果是第一次运行
npm start
```

### 3. 访问应用
打开浏览器访问：http://localhost:3000

## 功能测试清单

### 1. 页面导航测试
- [ ] 主页显示正常
- [ ] 左侧菜单中有"Excel材料处理"选项
- [ ] 点击菜单能正常跳转到Excel材料处理页面
- [ ] 页面标题和描述显示正确

### 2. 使用说明页面测试
- [ ] "使用说明"标签页显示正常
- [ ] 处理流程步骤显示完整
- [ ] 功能特性卡片布局正确
- [ ] Excel格式要求列表显示清晰
- [ ] 示例数据表格格式正确
- [ ] 价格优先级说明清楚
- [ ] 任务状态说明完整

### 3. 文件上传功能测试
- [ ] 切换到"文件上传"标签页
- [ ] 上传组件显示正常
- [ ] 只能选择Excel文件（.xlsx, .xls）
- [ ] 文件大小限制（50MB）正常工作
- [ ] 表单字段显示正确：
  - [ ] 标题行输入框
  - [ ] 批处理大小输入框
  - [ ] 保留天数输入框
  - [ ] 任务描述输入框
- [ ] 表单验证正常工作
- [ ] 上传按钮状态正确（选择文件后才能点击）

### 4. 文件上传流程测试
使用测试文件 `test_materials.xlsx`：

- [ ] 选择测试Excel文件
- [ ] 填写表单参数
- [ ] 点击"开始处理"按钮
- [ ] 显示上传成功消息
- [ ] 自动跳转到"任务监控"标签页
- [ ] 任务ID正确显示

### 5. 任务监控功能测试
- [ ] 任务监控页面显示正常
- [ ] 任务状态标签显示正确
- [ ] 进度条显示并更新
- [ ] 批次进度信息显示
- [ ] 处理时间线显示
- [ ] 任务信息区域显示
- [ ] 自动刷新功能正常（每3秒）
- [ ] 手动刷新按钮工作正常

### 6. 任务管理功能测试
- [ ] 切换到"任务管理"标签页
- [ ] 任务列表显示正常
- [ ] 表格列显示完整：
  - [ ] 任务ID（可复制）
  - [ ] 文件名
  - [ ] 状态标签
  - [ ] 进度条
  - [ ] 数据行数
  - [ ] 创建时间
  - [ ] 操作按钮
- [ ] 分页功能正常
- [ ] 刷新按钮工作正常

### 7. 任务操作功能测试
对于不同状态的任务，测试相应操作：

#### 处理中的任务
- [ ] "查看详情"按钮显示详情模态框
- [ ] "监控任务"按钮跳转到监控页面
- [ ] "删除任务"按钮显示确认框并能删除

#### 待合并的任务
- [ ] "合并结果"按钮能触发合并操作
- [ ] 合并后状态更新

#### 已完成的任务
- [ ] "下载结果"按钮能下载Excel文件
- [ ] 下载的文件名格式正确
- [ ] 文件内容包含处理结果

### 8. 任务详情模态框测试
- [ ] 点击"查看详情"打开模态框
- [ ] 统计信息显示正确
- [ ] 任务基本信息显示完整
- [ ] 处理统计数据显示（如果有）
- [ ] 关闭按钮正常工作

### 9. 响应式设计测试
- [ ] 桌面端显示正常
- [ ] 平板端布局适配
- [ ] 手机端显示和操作正常
- [ ] 表格在小屏幕上可横向滚动

### 10. 错误处理测试
- [ ] 上传非Excel文件显示错误提示
- [ ] 上传超大文件显示错误提示
- [ ] 网络错误时显示适当提示
- [ ] 任务不存在时显示错误信息
- [ ] API错误时显示错误详情

### 11. 用户体验测试
- [ ] 页面加载速度合理
- [ ] 操作反馈及时
- [ ] 加载状态显示清楚
- [ ] 成功/错误消息显示适当
- [ ] 界面美观，符合设计规范
- [ ] 操作流程直观易懂

## 测试数据

### 测试Excel文件内容
创建一个包含以下数据的Excel文件：

| 名称 | 型号 | 规格 | 数量 | 价格 | 备注 |
|------|------|------|------|------|------|
| 电缆 | YJV-3*120 | 3芯120平方 | 100 | 85.5 | 阻燃电缆 |
| 开关 | DZ47-63 | 63A单极 | 50 | 12.8 | 小型断路器 |
| 插座 | 86型 | 五孔插座 | 200 | 8.5 | 墙壁插座 |
| 灯具 | LED-40W | 40W面板灯 | 80 | 45.0 | 办公照明 |
| 管材 | PVC-25 | 25mm PVC管 | 500 | 3.2 | 电线管 |

### 测试参数
- 标题行：0
- 批处理大小：50
- 保留天数：30
- 任务描述：前端功能测试

## 预期结果

### 成功场景
1. 文件上传成功，显示任务ID
2. 任务监控页面显示处理进度
3. 任务列表中显示新任务
4. 任务状态从"已提交"→"处理中"→"待合并"→"已完成"
5. 最终能下载包含处理结果的Excel文件

### 错误场景
1. 上传非Excel文件时显示错误
2. 网络断开时显示连接错误
3. 任务失败时显示失败状态

## 性能要求
- 页面首次加载时间 < 3秒
- 文件上传响应时间 < 5秒
- 任务状态刷新间隔 3秒
- 表格数据加载时间 < 2秒

## 浏览器兼容性
测试以下浏览器：
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

## 问题记录

在测试过程中发现的问题请记录在此：

### 问题1
- **描述**：
- **重现步骤**：
- **预期结果**：
- **实际结果**：
- **严重程度**：
- **状态**：

### 问题2
- **描述**：
- **重现步骤**：
- **预期结果**：
- **实际结果**：
- **严重程度**：
- **状态**：

## 测试完成确认

- [ ] 所有功能测试项目已完成
- [ ] 所有发现的问题已记录
- [ ] 关键功能正常工作
- [ ] 用户体验满足要求
- [ ] 性能指标达标
- [ ] 浏览器兼容性良好

**测试人员**：_____________  
**测试日期**：_____________  
**测试版本**：_____________  
**测试结果**：□ 通过 □ 不通过  

## 备注
其他需要说明的问题或建议：
