.excel-material-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.excel-material-page .ant-typography-title {
  margin-bottom: 16px;
  color: #1677ff;
}

.excel-material-page .ant-typography-paragraph {
  margin-bottom: 24px;
  color: #666;
  font-size: 14px;
}

.upload-card {
  margin-bottom: 24px;
}

.upload-card .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.upload-card .ant-upload {
  width: 100%;
}

.upload-card .ant-upload-list {
  margin-top: 16px;
}

.upload-card .ant-btn-primary {
  height: 40px;
  font-size: 16px;
  font-weight: 500;
}

.upload-card .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.upload-card .ant-input-number {
  width: 100%;
}

/* 任务表格样式 */
.excel-material-page .ant-table {
  background: #fff;
  border-radius: 8px;
}

.excel-material-page .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.excel-material-page .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 状态标签样式 */
.excel-material-page .ant-tag {
  border-radius: 4px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 进度条样式 */
.excel-material-page .ant-progress {
  margin: 0;
}

.excel-material-page .ant-progress-text {
  font-size: 12px;
  font-weight: 500;
}

/* 操作按钮样式 */
.excel-material-page .ant-btn-text {
  padding: 4px 8px;
  height: auto;
  border-radius: 4px;
  transition: all 0.2s;
}

.excel-material-page .ant-btn-text:hover {
  background: #f0f0f0;
}

.excel-material-page .ant-btn-text.ant-btn-dangerous:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 模态框样式 */
.excel-material-page .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.excel-material-page .ant-modal-title {
  font-weight: 600;
  color: #262626;
}

.excel-material-page .ant-descriptions {
  margin-top: 16px;
}

.excel-material-page .ant-descriptions-item-label {
  font-weight: 500;
  color: #595959;
  background: #fafafa;
}

.excel-material-page .ant-descriptions-item-content {
  color: #262626;
}

/* 统计卡片样式 */
.excel-material-page .ant-statistic {
  text-align: center;
}

.excel-material-page .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.excel-material-page .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

/* 警告信息样式 */
.excel-material-page .ant-alert {
  border-radius: 6px;
}

.excel-material-page .ant-alert-info {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.excel-material-page .ant-alert-info .ant-alert-icon {
  color: #52c41a;
}

/* 标签页样式 */
.excel-material-page .ant-tabs {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.excel-material-page .ant-tabs-tab {
  font-weight: 500;
  font-size: 16px;
}

.excel-material-page .ant-tabs-tab-active {
  color: #1677ff;
}

.excel-material-page .ant-tabs-ink-bar {
  background: #1677ff;
}

.excel-material-page .ant-tabs-content-holder {
  padding-top: 24px;
}

/* 表单样式优化 */
.excel-material-page .ant-form-item {
  margin-bottom: 24px;
}

.excel-material-page .ant-form-item-label {
  padding-bottom: 8px;
}

.excel-material-page .ant-form-item-label > label {
  height: auto;
  line-height: 1.5;
}

.excel-material-page .ant-input,
.excel-material-page .ant-input-number,
.excel-material-page .ant-select-selector {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s;
}

.excel-material-page .ant-input:hover,
.excel-material-page .ant-input-number:hover,
.excel-material-page .ant-select-selector:hover {
  border-color: #4096ff;
}

.excel-material-page .ant-input:focus,
.excel-material-page .ant-input-number:focus,
.excel-material-page .ant-select-focused .ant-select-selector {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* 上传组件样式 */
.excel-material-page .ant-upload-list-item {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  background: #fafafa;
}

.excel-material-page .ant-upload-list-item-info {
  padding: 8px 12px;
}

.excel-material-page .ant-upload-list-item-name {
  color: #262626;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .excel-material-page {
    padding: 16px;
  }
  
  .excel-material-page .ant-tabs {
    padding: 16px;
  }
  
  .excel-material-page .ant-table {
    font-size: 12px;
  }
  
  .excel-material-page .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .excel-material-page .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

/* 加载状态样式 */
.excel-material-page .ant-spin-container {
  min-height: 200px;
}

.excel-material-page .ant-spin-dot {
  color: #1677ff;
}

/* 空状态样式 */
.excel-material-page .ant-empty {
  padding: 40px 0;
}

.excel-material-page .ant-empty-description {
  color: #8c8c8c;
}

/* 工具提示样式 */
.excel-material-page .ant-tooltip-inner {
  background: #262626;
  border-radius: 4px;
  font-size: 12px;
}

.excel-material-page .ant-tooltip-arrow::before {
  background: #262626;
}

/* 确认框样式 */
.excel-material-page .ant-popconfirm-inner-content {
  padding: 12px 16px;
}

.excel-material-page .ant-popconfirm-message {
  font-size: 14px;
  color: #262626;
}

.excel-material-page .ant-popconfirm-buttons {
  margin-top: 12px;
}

/* 代码文本样式 */
.excel-material-page .ant-typography-copy {
  color: #1677ff;
}

.excel-material-page .ant-typography-copy:hover {
  color: #4096ff;
}

/* 卡片样式优化 */
.excel-material-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.excel-material-page .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.excel-material-page .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.excel-material-page .ant-card-body {
  padding: 24px;
}

/* 行间距优化 */
.excel-material-page .ant-row {
  margin-bottom: 0;
}

.excel-material-page .ant-col {
  margin-bottom: 0;
}
