# 价格库系统配置文件

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "************************************************/price_db")
DATABASE_URL = "************************************************/price_db"

# AI配置
# Dify API配置
DIFY_API_URL = os.getenv("DIFY_API_URL", "https://dify.shecc.com")
DIFY_API_KEY = os.getenv("DIFY_API_KEY", "app-71f51KUm0g24lKUU6u0fmG2o")

# 聊天模型配置
CHAT_API_URL = os.getenv("CHAT_API_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
CHAT_API_KEY = os.getenv("CHAT_API_KEY", "sk-b4a9a52994be4bbaabbc8f4c27ad2aec")  # 聊天API密钥
CHAT_MODEL = os.getenv("CHAT_MODEL", "deepseek-v3")
CHAT_TEMPERATURE = float(os.getenv("CHAT_TEMPERATURE", "0.7"))
CHAT_MAX_TOKENS = int(os.getenv("CHAT_MAX_TOKENS", "2048"))

EMBEDDING_API_URL_DEFAULT = os.getenv("CHAT_API_URL", "http://************:11434/api")
EMBEDDING_API_KEY_DEFAULT = os.getenv("CHAT_API_KEY", None)  # 聊天API密钥
EMBEDDING_MODEL_DEFAULT = os.getenv("CHAT_MODEL", "nomic-embed-text:latest")
CHAT_TEMPERATURE = float(os.getenv("CHAT_TEMPERATURE", "0.7"))
CHAT_MAX_TOKENS = int(os.getenv("CHAT_MAX_TOKENS", "2048"))

# 嵌入模型配置
# 如果没有配置嵌入模型的URL，则默认使用聊天模型的URL
EMBEDDING_API_URL_ENV = os.getenv("EMBEDDING_API_URL",EMBEDDING_API_URL_DEFAULT)
EMBEDDING_API_URL = EMBEDDING_API_URL_ENV if EMBEDDING_API_URL_ENV else CHAT_API_URL

# 如果没有配置嵌入模型的API密钥，则默认使用聊天模型的API密钥
EMBEDDING_API_KEY_ENV = os.getenv("EMBEDDING_API_KEY",EMBEDDING_API_KEY_DEFAULT)
EMBEDDING_API_KEY = EMBEDDING_API_KEY_ENV if EMBEDDING_API_KEY_ENV else CHAT_API_KEY

EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL",EMBEDDING_MODEL_DEFAULT)
EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "32"))
DEFAULT_EMBEDDING_MODEL = os.getenv("DEFAULT_EMBEDDING_MODEL", "nomic-embed-text:latest")

# 兼容旧版配置（向后兼容）
AI_API_URL = EMBEDDING_API_URL  # 默认使用嵌入API URL作为通用AI API URL
AI_API_KEY = EMBEDDING_API_KEY  # 默认使用嵌入API KEY作为通用AI API KEY

# AI功能配置
ENABLE_SEMANTIC_SEARCH = os.getenv("ENABLE_SEMANTIC_SEARCH", "True").lower() == "true"
ENABLE_CHAT_ASSISTANT = os.getenv("ENABLE_CHAT_ASSISTANT", "True").lower() == "true"

# 应用配置
APP_HOST = os.getenv("APP_HOST", "0.0.0.0")
APP_PORT = int(os.getenv("APP_PORT", "8000"))
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# 文件上传配置
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 向量数据库配置
VECTOR_DIMENSION = 1024  # nomic-embed-text模型的嵌入维度
VECTOR_DB_TYPE = os.getenv("VECTOR_DB_TYPE", "milvus").lower() # 'milvus' or 'weaviate'
USE_LOCAL_EMBEDDING = os.getenv("USE_LOCAL_EMBEDDING", "True").lower() == "true"  # 是否使用本地CPU向量计算
NOMIC_MODEL_NAME = os.getenv("NOMIC_MODEL_NAME", "nomic-embed-text:latest")  # nomic模型名称
USE_NOMIC_EMBEDDING = os.getenv("USE_NOMIC_EMBEDDING", "False").lower() == "true"  # 是否使用nomic模型

# BGE-M3混合嵌入模型配置
USE_BGE_M3_EMBEDDING = os.getenv("USE_BGE_M3_EMBEDDING", "True").lower() == "true"  # 是否使用BGE-M3混合嵌入
BGE_M3_MODEL_NAME = os.getenv("BGE_M3_MODEL_NAME", "BAAI/bge-m3")  # BGE-M3模型名称
# 混合嵌入配置
ENABLE_HYBRID_SEARCH = os.getenv("ENABLE_HYBRID_SEARCH", "True").lower() == "true"  # 是否启用混合搜索
DENSE_WEIGHT = float(os.getenv("DENSE_WEIGHT", "0.5"))  # 稠密向量权重
SPARSE_WEIGHT = float(os.getenv("SPARSE_WEIGHT", "0.5"))  # 稀疏向量权重

# 添加本地BGE-M3嵌入配置
LOCAL_BGE_M3_EMBEDDING = os.getenv("LOCAL_BGE_M3_EMBEDDING", "True").lower() == "true"  # 是否使用本地BGE-M3嵌入
BGE_M3_BATCH_SIZE = int(os.getenv("BGE_M3_BATCH_SIZE", "8"))  # BGE-M3批处理大小，本地计算时可能需要较小的值

# Milvus配置 (如果 VECTOR_DB_TYPE 为 'milvus')
MILVUS_HOST = os.getenv("MILVUS_HOST", "************") # Milvus 服务地址
MILVUS_PORT = os.getenv("MILVUS_PORT", "19530") # Milvus 服务端口
MILVUS_COLLECTION_NAME = os.getenv("MILVUS_COLLECTION_NAME", "materials") # Milvus 集合名称e
# MILVUS_USER = os.getenv("MILVUS_USER", "") # 如果 Milvus 开启了认证
# MILVUS_PASSWORD = os.getenv("MILVUS_PASSWORD", "") # 如果 Milvus 开启了认证

# 搜索分数阈值配置
SEARCH_SCORE_THRESHOLD = float(os.getenv("SEARCH_SCORE_THRESHOLD", "0.6"))

# Weaviate配置 (如果 VECTOR_DB_TYPE 为 'weaviate')
# USE_WEAVIATE = os.getenv("USE_WEAVIATE", "True").lower() == "true" # 由 VECTOR_DB_TYPE 控制
WEAVIATE_URL = os.getenv("WEAVIATE_URL", "http://***********:8080")
WEAVIATE_API_KEY = os.getenv("WEAVIATE_API_KEY", "")
WEAVIATE_CLASS_NAME = os.getenv("WEAVIATE_CLASS_NAME", "materials")

# Redis和RQ配置
REDIS_HOST = os.getenv("REDIS_HOST", "***********")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "yhb25IEz")
REDIS_DB = os.getenv("REDIS_DB", "3")
REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# RQ Dashboard配置
RQ_DASHBOARD_HOST = os.getenv("RQ_DASHBOARD_HOST", "0.0.0.0")
RQ_DASHBOARD_PORT = int(os.getenv("RQ_DASHBOARD_PORT", "9181"))
RQ_DASHBOARD_USERNAME = os.getenv("RQ_DASHBOARD_USERNAME", "")
RQ_DASHBOARD_PASSWORD = os.getenv("RQ_DASHBOARD_PASSWORD", "")
