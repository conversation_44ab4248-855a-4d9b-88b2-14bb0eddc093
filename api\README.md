# API 服务文档

## 项目概述
本API服务提供Excel文件处理、报价单分析、价格验证和填充等功能，基于FastAPI框架开发。

## 主要功能
- Excel文件读取与处理
- 报价单自动识别与分析
- 价格验证与填充
- 异步任务处理

## API接口说明

### 报价单处理接口
`POST /excel/read-quotation`

**请求参数**:
- `file`: 上传的Excel文件
- `base_info`: JSON字符串，包含基础信息

**示例请求**:
```bash
curl -X POST "http://localhost:8000/excel/read-quotation" \
  -H "accept: application/json" \
  -F "file=@报价单.xlsx" \
  -F "base_info='{\"project\":\"项目名称\",\"market\":\"市场行情\"}'"
```

**响应示例**:
```json
{
  "task_id": "12345",
  "status": "pending",
  "message": "报价单处理任务已提交"
}
```

### 任务查询接口
`GET /tasks/{task_id}`

**响应示例**:
```json
{
  "status": "completed",
  "results": [
    {
      "row_data": {...},
      "validation_result": {
        "is_valid": true,
        "price_range": "1000-1200",
        "analysis": "价格合理，符合市场行情"
      }
    }
  ]
}
```

## 报价单处理流程
1. 自动识别包含"报价单"的工作表
2. 分析每行数据：
   - 有价格：验证价格合理性
   - 无价格：填充最接近价格
3. 调用Dify API进行价格分析
4. 返回处理结果

## 任务队列系统 (Flask-RQ)

本系统使用Flask-RQ实现异步任务处理，工作原理如下：

1. **任务提交**:
   - API接收到请求后，将耗时操作封装为任务
   - 任务被推送到Redis队列中
   - 立即返回任务ID给客户端

2. **任务处理**:
   - RQ Worker进程从Redis队列获取任务
   - 执行任务逻辑（如报价单处理）
   - 将结果存储到Redis中

3. **状态查询**:
   - 客户端通过任务ID查询状态
   - 系统从Redis获取任务状态和结果

4. **重试机制**:
   - 任务失败会自动重试
   - 支持设置最大重试次数

## 环境配置
1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置环境变量：
```env
DIFY_API_URL=your_dify_api_url
DIFY_API_KEY=your_api_key
REDIS_URL=redis://localhost:6379/0  # Redis连接地址
```

## 运行方式
```bash
uvicorn main:app --reload
```

## 测试
```bash
pytest test_excel.py
```

## 注意事项
1. 报价单Excel文件需包含"报价单"关键字的工作表
2. 基础信息JSON需包含项目和市场行情等关键信息
3. 价格验证和填充可能需要较长时间，建议异步处理
