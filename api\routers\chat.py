from fastapi import APIRouter, Request, Response, HTTPException, Depends
from fastapi.responses import StreamingResponse
import json
from typing import AsyncGenerator, Dict, Any, Optional
from ..config import CHAT_MODEL, CHAT_TEMPERATURE, CHAT_MAX_TOKENS, ENABLE_SEMANTIC_SEARCH, ENABLE_CHAT_ASSISTANT
from ..core.logging_config import get_logger
from ..core.vector_db import get_vector_db_client
from ..core.api_client import APIClient
from ..crud.item import get_items_by_semantic_search
        # 使用DifyClient处理聊天请求
from ..core.dify_client import DifyClient
router = APIRouter()
logger = get_logger("chat_api")
vector_db = get_vector_db_client()

async def extract_product_info(user_input: str) -> dict:
    """
    使用大模型从用户输入中提取商品信息
    """
    try:
        # 调用大模型提取商品信息
        response = await vector_db.extract_info(user_input)
        return {
            'model': response.get('model'),
            'unit_price': response.get('price'),
            'name': response.get('name'),
        }
    except Exception as e:
        logger.error(f"提取商品信息时出错: {str(e)}")
        return {}

@router.post("/chat")
@router.get("/chat")
async def chat(request: Request) -> StreamingResponse:
    """
    聊天API，支持流式响应
    """
    try:
        # 获取请求体
        body = await request.json()
        messages = body.get("messages", [])
        model = CHAT_MODEL #body.get("model", CHAT_MODEL)
        temperature = body.get("temperature", CHAT_TEMPERATURE)
        
        if not messages:
            raise HTTPException(status_code=400, detail="消息不能为空")
        
        # 提取最后一条用户消息
        last_user_message = messages[-1]['content'] if messages else ''
        
        # 如果启用了语义搜索功能，则提取商品信息并进行搜索
        if ENABLE_SEMANTIC_SEARCH:
            # 提取商品信息
            product_info = await extract_product_info(last_user_message)
            
            # 如果提取到商品信息，进行语义搜索
            if product_info and (product_info.get('name') or product_info.get('model') or product_info.get('unit_price')):
                logger.info(f"提取到商品信息: {product_info}，准备进行语义搜索")
                # Removed await as get_items_by_semantic_search is synchronous
                search_results = await get_items_by_semantic_search(
                       query=product_info.get('name'),
                    model=product_info.get('model'),
                    unit_price=product_info.get('unit_price'),
                 
                )
                
                if search_results:
                    # 找到相关商品信息，添加到系统消息中
                    messages.append({
                        'role': 'system',
                        'content': f"找到以下相关商品: {json.dumps(search_results)}"
                    })
                else:
                    # 未找到相关商品信息，添加明确的提示
                    messages.append({
                        'role': 'system',
                        'content': f"未找到与'{product_info.get('model') or ''}' 相关的商品信息。请告知用户：'暂时没有收录此信息'，不要提供不确定的回答。"
                    })
                    logger.info(f"未找到与'{product_info.get('model') or ''}' 相关的商品信息")
            elif '价格' in last_user_message or '多少钱' in last_user_message or '型号' in last_user_message:
                # 用户可能在询问价格或型号，但未能提取到有效信息
                messages.append({
                    'role': 'system',
                    'content': "用户似乎在询问商品信息，但未能提取到有效的商品型号或价格。请告知用户：'暂时没有收录此信息'，不要提供不确定的回答。"
                })
                logger.info("用户可能在询问商品信息，但未能提取到有效信息")
        
        logger.info(f"收到聊天请求: model={model}, messages_count={len(messages)}")
        
        # 创建流式响应
        return StreamingResponse(
            stream_chat_response(messages, model, temperature),
            media_type="text/event-stream"
        )
    except Exception as e:
        logger.error(f"处理聊天请求时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理请求失败: {str(e)}")

async def stream_chat_response(messages: list, model: str, temperature: float) -> AsyncGenerator[str, None]:
    """
    流式生成聊天响应
    """
    # 使用APIClient处理聊天流式响应
    from ..core.api_client import APIClient
    async for chunk in APIClient.stream_chat_response(messages, model, temperature):
        yield chunk

@router.post("/dify-chat")
async def dify_chat(request: Request) -> StreamingResponse:
    """
    使用Dify API进行聊天交互
    """
    try:
        # 获取请求体
        body = await request.json()
        query = body.get("query", "")
     
        response_mode = body.get("response_mode", "streaming")
        conversation_id = body.get("conversation_id", "")
        user = body.get("user", "")
        files = body.get("files", [])

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        async def stream_response():
            try:
                async for chunk in DifyClient.chat(query, response_mode, conversation_id, user):
                    yield chunk
            except Exception as e:
                logger.error(f"处理Dify聊天流式响应时出错: {str(e)}")
                # 确保错误响应符合SSE格式规范
                yield f"data: {json.dumps({'error': str(e)})}"

        return StreamingResponse(
            stream_response(),
            media_type="text/event-stream"
        )
    except Exception as e:
        logger.error(f"处理Dify聊天请求时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理请求失败: {str(e)}")
