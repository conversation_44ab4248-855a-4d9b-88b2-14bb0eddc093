import logging
import sys
import os
from datetime import datetime
from pathlib import Path

# 创建日志目录
LOG_DIR = Path("d:/Codes/materials/logs")
LOG_DIR.mkdir(exist_ok=True)

# 日志文件名格式：年月日.log
log_filename = LOG_DIR / f"{datetime.now().strftime('%Y-%m-%d')}.log"

# 自定义日志级别
logging.SUCCESS = 25  # 介于INFO和WARNING之间
logging.addLevelName(logging.SUCCESS, 'SUCCESS')

# 扩展Logger类添加success方法
class CustomLogger(logging.Logger):
    def success(self, msg, *args, **kwargs):
        self.log(logging.SUCCESS, msg, *args, **kwargs)

# 注册自定义Logger类
logging.setLoggerClass(CustomLogger)

# 配置日志格式
LOG_FORMAT = "%(asctime)s [%(levelname)s] %(name)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))

# 文件处理器
file_handler = logging.FileHandler(log_filename, encoding='utf-8')
file_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))

# 配置根日志记录器
def configure_logging(debug_mode=False):
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG if debug_mode else logging.INFO)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加处理器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    
    return root_logger

# 获取应用日志记录器
def get_logger(name):
    return logging.getLogger(name)