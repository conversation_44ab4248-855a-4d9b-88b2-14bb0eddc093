# API客户端模块，集中处理各种API请求

import requests
import json
import time
import asyncio
import os
from typing import List, Dict, Any, Optional, Union, AsyncGenerator
from openai import OpenAI
from ..config import (
    EMBEDDING_API_URL, EMBEDDING_API_KEY, EMBEDDING_MODEL,
    CHAT_API_URL, CHAT_API_KEY, CHAT_MODEL, CHAT_MAX_TOKENS
)
from ..core.logging_config import get_logger
from tenacity import retry, wait_exponential, stop_after_attempt

logger = get_logger("api_client")

# 从环境变量获取调试模式设置，默认为False
API_DEBUG_MODE = os.getenv("API_DEBUG_MODE", "False").lower() == "true"

# 调试日志最大长度限制
DEBUG_LOG_MAX_LENGTH = int(os.getenv("DEBUG_LOG_MAX_LENGTH", "1000"))


class APIClient:
    """API客户端，处理所有外部API请求"""
    
    # 类属性，控制是否启用调试模式
    debug_mode = API_DEBUG_MODE
    
    @classmethod
    def set_debug_mode(cls, enabled: bool):
        """设置调试模式
        
        Args:
            enabled: 是否启用调试模式
        """
        cls.debug_mode = enabled
        logger.info(f"API客户端调试模式已{'启用' if enabled else '禁用'}")
    
    @classmethod
    def _sanitize_response_data(cls, data: Any) -> Any:
        """清理响应数据中的敏感信息
        
        Args:
            data: 原始响应数据
            
        Returns:
            Any: 清理后的响应数据
        """
        # 如果是字典，递归处理
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                # 检查是否为敏感字段
                if key.lower() in ['authorization', 'api_key', 'apikey', 'key', 'secret', 'password', 'token']:
                    result[key] = '*** REDACTED ***'
                else:
                    result[key] = cls._sanitize_response_data(value)
            return result
        # 如果是列表，递归处理每个元素
        elif isinstance(data, list):
            return [cls._sanitize_response_data(item) for item in data]
        # 其他类型直接返回
        else:
            return data
    
    @classmethod
    def log_request(cls, method: str, url: str, headers: dict, data: Any):
        """记录API请求信息
        
        Args:
            method: 请求方法
            url: 请求URL
            headers: 请求头
            data: 请求数据
        """
        if not cls.debug_mode:
            return
            
        # 创建请求头的副本，移除敏感信息
        safe_headers = headers.copy() if headers else {}
        if 'Authorization' in safe_headers:
            safe_headers['Authorization'] = '*** REDACTED ***'
        
        # 清理请求数据中的敏感信息
        safe_data = cls._sanitize_response_data(data)
            
        logger.debug(f"API请求 [{method}] {url}")
        logger.debug(f"请求头: {json.dumps(safe_headers, ensure_ascii=False, indent=2)}")
        
        # 限制请求数据的日志长度
        data_json = json.dumps(safe_data, ensure_ascii=False, indent=2)
        if len(data_json) > DEBUG_LOG_MAX_LENGTH:
            logger.debug(f"请求数据(截断): {data_json[:DEBUG_LOG_MAX_LENGTH]}... (完整长度: {len(data_json)})")
        else:
            logger.debug(f"请求数据: {data_json}")
    
    @classmethod
    def log_response(cls, url: str, status_code: int, response_data: Any, latency: float):
        """记录API响应信息
        
        Args:
            url: 请求URL
            status_code: 响应状态码
            response_data: 响应数据
            latency: 请求延迟时间(秒)
        """
        if not cls.debug_mode:
            return
            
        logger.debug(f"API响应 [{status_code}] {url} (耗时: {latency:.2f}秒)")
        
        # 如果响应数据是字符串，直接记录
        if isinstance(response_data, str):
            try:
                if len(response_data) > DEBUG_LOG_MAX_LENGTH:
                    logger.debug(f"响应数据(截断): {response_data[:DEBUG_LOG_MAX_LENGTH]}... (完整长度: {len(response_data)})")
                else:
                    logger.debug(f"响应数据: {response_data}")
            except Exception as e:
                logger.debug(f"响应数据(无法记录): {str(e)}")
        else:
            # 对于对象类型，转换为JSON字符串
            try:
                # 处理可能包含敏感信息的响应
                safe_data = cls._sanitize_response_data(response_data)
                json_str = json.dumps(safe_data, ensure_ascii=False, indent=2)
                if len(json_str) > DEBUG_LOG_MAX_LENGTH:
                    logger.debug(f"响应数据(截断): {json_str[:DEBUG_LOG_MAX_LENGTH]}... (完整长度: {len(json_str)})")
                else:
                    logger.debug(f"响应数据: {json_str}")
            except Exception as e:
                logger.debug(f"响应数据(无法序列化): {str(e)}")
    
    @classmethod
    def log_stream_chunk(cls, chunk: dict):
        """记录流式响应的数据块
        
        Args:
            chunk: 流式响应的数据块
        """
        if not cls.debug_mode:
            return
            
        try:
            # 清理数据块中的敏感信息
            safe_chunk = cls._sanitize_response_data(chunk)
            chunk_json = json.dumps(safe_chunk, ensure_ascii=False)
            
            # 限制日志长度
            if len(chunk_json) > DEBUG_LOG_MAX_LENGTH:
                logger.debug(f"流式响应块(截断): {chunk_json[:DEBUG_LOG_MAX_LENGTH]}... (完整长度: {len(chunk_json)})")
            else:
                logger.debug(f"流式响应块: {chunk_json}")
        except Exception as e:
            logger.debug(f"流式响应块(无法序列化): {str(e)}")
    
    @classmethod
    @retry(wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3))
    def generate_embedding(cls, text: str, model_name: str) -> list:
        """生成文本的嵌入向量
        
        Args:
            text: 需要生成嵌入向量的文本
            model_name: 使用的嵌入模型名称
            
        Returns:
            list: 嵌入向量
        """
        # 处理空文本
        if not text.strip():
            logger.warning("无法生成空文本的嵌入向量")
            return []
            
        # 调用嵌入模型API
        try:
            logger.debug(f"生成嵌入向量请求参数: model={model_name}, text_length={len(text)}")
            start_time = time.time()
            
            # 准备请求头，如果有API密钥则添加到请求头中
            headers = {}
            if EMBEDDING_API_KEY:
                headers["Authorization"] = f"Bearer {EMBEDDING_API_KEY}"
            
            # 准备请求数据
            request_data = {"model": model_name, "prompt": text}
            request_url = f"{EMBEDDING_API_URL}/embeddings"
            
            # 记录请求信息
            cls.log_request("POST", request_url, headers, request_data)
                
            response = requests.post(
                request_url,
                json=request_data,
                headers=headers,
                timeout=30
            )
            
            latency = time.time() - start_time
            
            if response.status_code != 200:
                logger.error(f"嵌入API异常响应: {response.status_code} - {response.text}")
                # 记录错误响应
                cls.log_response(request_url, response.status_code, response.text, latency)
                raise ValueError(f"API响应异常: {response.status_code}")

            result = response.json()
            # 记录响应信息
            cls.log_response(request_url, response.status_code, result, latency)
            
            if 'embedding' not in result:
                logger.error("API响应缺少embedding字段")
                raise ValueError("无效的API响应格式")

            logger.debug(f"嵌入生成成功，耗时{latency:.2f}秒，向量维度: {len(result['embedding'])}")
            return result['embedding']

        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"生成嵌入向量时发生未处理异常: {str(e)}")
            raise
    
    @classmethod
    async def extract_info(cls, text: str) -> dict:
        """从文本中提取商品信息
        
        Args:
            text: 用户输入的文本
            
        Returns:
            dict: 包含提取出的商品信息，如型号、价格等
        """
        try:
            # 构建提示词，引导大模型提取商品信息
            prompt = f"""请从以下文本中提取商品信息，仅返回JSON格式，包含name(名称)、model(型号)和unit_price(单价)字段。如果无法提取，则对应字段返回null。

文本：{text}

仅返回JSON格式如：{{"model": "提取的型号", "unit_price": 提取的单价,"name":"提取的商品名称"}}，不要有其他内容。"""
            
            # 创建OpenAI客户端
            client = OpenAI(api_key=CHAT_API_KEY, base_url=CHAT_API_URL)
            
            # 准备请求数据
            messages = [
                {"role": "system", "content": "你是一个专门提取商品信息的助手，只返回JSON格式的结果。"},
                {"role": "user", "content": prompt}
            ]
            
            # 记录请求信息
            request_url = f"{CHAT_API_URL}/chat/completions"
            headers = {"Authorization": f"Bearer {CHAT_API_KEY}"} if CHAT_API_KEY else {}
            data = {
                "model": CHAT_MODEL,
                "messages": messages,
                "temperature": 0.2,
                "max_tokens": 200
            }
            start_time = time.time()
            cls.log_request("POST", request_url, headers, data)
            
            # 使用OpenAI客户端发送请求
            response = await asyncio.to_thread(
                lambda: client.chat.completions.create(
                    model=CHAT_MODEL,
                    messages=messages,
                    temperature=0.2,
                    max_tokens=200
                )
            )
            
            latency = time.time() - start_time
            
            # 记录响应信息
            result = {"choices": [{"message": {"content": response.choices[0].message.content}}]}
            cls.log_response(request_url, 200, result, latency)
            
            # 获取响应内容
            content = response.choices[0].message.content
            logger.info(f"OpenAi Api 返回信息: {content}")
            # 尝试从内容中提取JSON
            try:
                # 清理内容，处理可能的多行JSON响应
                # 移除所有换行符和额外的空格，只保留JSON部分
                content = content.strip()
                
                # 查找JSON内容的开始和结束位置
                start = content.find('{')
                end = content.rfind('}')
                
                if start != -1 and end != -1 and end > start:
                    json_str = content[start:end+1]
                    # 尝试解析JSON
                    extracted_info = json.loads(json_str)
                    logger.info(f"成功提取商品信息: {extracted_info}")
                    return extracted_info
                else:
                    logger.warning(f"无法从响应中找到JSON内容: {content}")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"解析提取的JSON时出错: {e}, 内容: {content}")
                # 尝试更严格的JSON提取方法
                try:
                    import re
                    # 使用正则表达式查找JSON对象
                    json_match = re.search(r'\{[^{}]*\}', content)
                    if json_match:
                        json_str = json_match.group(0)
                        extracted_info = json.loads(json_str)
                        logger.info(f"使用正则表达式成功提取商品信息: {extracted_info}")
                        return extracted_info
                except Exception as regex_error:
                    logger.error(f"使用正则表达式提取JSON失败: {regex_error}")
                return {}
            
        except Exception as e:
            logger.error(f"提取商品信息时出错: {str(e)}")
            return {}
    
    @classmethod
    async def stream_chat_response(cls, messages: list, model: str, temperature: float) -> AsyncGenerator[str, None]:
        """流式生成聊天响应
        
        Args:
            messages: 聊天消息列表
            model: 使用的模型名称
            temperature: 温度参数
            
        Yields:
            str: 流式响应的文本片段
        """
        try:
            # 创建OpenAI客户端
            client = OpenAI(api_key=CHAT_API_KEY, base_url=CHAT_API_URL)
            
            # 准备请求数据用于日志记录
            data = {
                "model": model,
                "messages": messages,
                "stream": True,
                "temperature": temperature,
                "max_tokens": CHAT_MAX_TOKENS
            }
            
            # 记录请求信息
            request_url = f"{CHAT_API_URL}/chat/completions"
            headers = {"Authorization": f"Bearer {CHAT_API_KEY}"} if CHAT_API_KEY else {}
            start_time = time.time()
            cls.log_request("POST", request_url, headers, data)
            
            try:
                # 使用OpenAI客户端发送流式请求
                stream = await asyncio.to_thread(
                    lambda: client.chat.completions.create(
                        model=model,
                        messages=messages,
                        stream=True,
                        temperature=temperature,
                        max_tokens=CHAT_MAX_TOKENS
                    )
                )
                
                latency = time.time() - start_time
                # 记录初始响应信息
                cls.log_response(request_url, 200, "开始接收流式响应", latency)
                
                # 处理流式响应
                # OpenAI的Stream对象不支持异步迭代，需要使用异步方式处理
                # 创建一个异步生成器来包装同步迭代
                async def async_iterate():
                    for chunk in stream:
                        yield chunk
                
                async for chunk in async_iterate():
                    try:
                        # 记录流式响应块
                        chunk_data = {
                            "id": chunk.id,
                            "model": chunk.model,
                            "choices": [{
                                "delta": {
                                    "content": chunk.choices[0].delta.content or ""
                                }
                            }]
                        }
                        cls.log_stream_chunk(chunk_data)
                        
                        # 提取文本内容
                        content = chunk.choices[0].delta.content or ""
                        if content:
                            # 发送SSE格式的响应
                            yield f"data: {json.dumps({'content': content})}\n\n"
                    except Exception as e:
                        logger.error(f"处理流式响应块时出错: {str(e)}")
                        yield f"data: {json.dumps({'error': str(e)})}\n\n"
                
                # 发送完成信号
                yield f"data: [DONE]\n\n"
                
            except Exception as e:
                error_msg = f"生成聊天响应时出错: {str(e)}"
                logger.error(error_msg)
                yield f"data: {json.dumps({'error': error_msg})}\n\n"
        except Exception as e:
            logger.error(f"创建流式响应时出错: {str(e)}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"


# 创建单例实例
_api_client = None

def get_api_client() -> APIClient:
    """获取API客户端实例"""
    global _api_client
    if _api_client is None:
        _api_client = APIClient()
    return _api_client
