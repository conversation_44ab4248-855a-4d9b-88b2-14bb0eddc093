{"version": "0.2.0", "configurations": [{"name": "Python: FastAPI", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"], "jinja": true, "justMyCode": false, "env": {"DEBUG": "True", "LOG_LEVEL": "DEBUG", "SHOW_SQL": "True", "ENABLE_PROFILING": "True", "LOG_REQUESTS": "True", "INCLUDE_STACK_TRACE": "True"}}, {"name": "Python: 当前文件", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true}, {"name": "Python: 测试", "type": "python", "request": "launch", "module": "pytest", "args": ["-v"], "justMyCode": false}, {"name": "Python: Flower调试", "type": "python", "request": "launch", "program": "${workspaceFolder}/flower_debug.py", "console": "integratedTerminal", "justMyCode": false, "env": {"DEBUG": "True", "LOG_LEVEL": "DEBUG", "PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Flower", "type": "python", "request": "launch", "program": "${workspaceFolder}/api_flower.py", "args": ["--host", "localhost", "--debug"], "justMyCode": false, "env": {"DEBUG": "True", "LOG_LEVEL": "DEBUG", "PYTHONPATH": "${workspaceFolder}"}}], "compounds": []}