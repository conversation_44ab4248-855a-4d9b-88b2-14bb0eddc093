import React, { useState, useCallback } from 'react';
import { Input, Table, Spin, Alert, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';

const { Search } = Input;

// 定义向量搜索结果类型
interface VectorSearchResult {
  id?: number;
  product_name?: string;
  unit_price?: number;
  specification?: string;
  brand?: string;
  origin?: string;
  score: number;
}

const VectorSearchPage: React.FC = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<VectorSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = useCallback(async (value: string) => {
    if (!value || value.trim() === '') {
      setResults([]);
      setError(null);
      return;
    }
    setQuery(value);
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({ q: value, limit: '20' }); // 默认限制20条
      const response = await axios.get(`/api/items/search?${params.toString()}`);
      setResults(response.data.results || []);
    } catch (err: any) {
      console.error("Vector search failed:", err);
      setError(err.response?.data?.detail || err.message || '搜索失败，请稍后重试');
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const columns: ColumnsType<VectorSearchResult> = [
    {
      title: '得分',
      dataIndex: 'score',
      key: 'score',
      render: (score: number) => score.toFixed(4), // 保留4位小数
      sorter: (a, b) => b.score - a.score, // 默认按得分降序
      defaultSortOrder: 'descend',
      width: 100,
    },
    { title: '产品名称', dataIndex: 'product_name', key: 'product_name' },
    { title: '规格型号', dataIndex: 'specification', key: 'specification' },
    { title: '品牌', dataIndex: 'brand', key: 'brand' },
    { title: '产地', dataIndex: 'origin', key: 'origin' },
    { title: '单价', dataIndex: 'unit_price', key: 'unit_price' },
    // 可以根据需要添加更多列，例如 ID
    // { title: 'ID', dataIndex: 'id', key: 'id' },
  ];

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <h2>向量检索</h2>
      <Search
        placeholder="输入关键词进行向量搜索"
        enterButton="搜索"
        size="large"
        onSearch={handleSearch}
        loading={loading}
        allowClear
      />

      {error && <Alert message="搜索出错" description={error} type="error" showIcon closable onClose={() => setError(null)} />}

      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={results}
          rowKey={(record) => `${record.id || Math.random()}-${record.score}`} // 确保 key 唯一
          bordered
          pagination={false} // 不分页
          style={{ marginTop: 16 }}
        />
      </Spin>
    </Space>
  );
};

export default VectorSearchPage;
