# Flask-RQ2 使用指南

本项目已成功集成 Flask-RQ2，提供了更简洁和强大的任务队列管理功能。

## 主要改进

### 1. 使用 Flask-RQ2 替代原生 RQ

- **更简洁的配置**: 使用 Flask-RQ2 的配置管理
- **装饰器支持**: 提供 `@job` 装饰器来定义任务
- **更好的集成**: 与 Flask 生态系统更好的集成
- **向后兼容**: 保持与现有代码的兼容性

### 2. 配置文件更新

文件: `api/core/rq_app.py`

```python
from flask_rq2 import RQ

# 创建Flask-RQ2实例
rq = RQ()

# 配置Redis连接
rq.init_app(None, {
    'RQ_REDIS_URL': REDIS_URL,
    'RQ_QUEUES': ['default', 'high', 'low'],
    'RQ_DEFAULT_TIMEOUT': 300,
    'RQ_EXCEPTION_HANDLERS': [],
})
```

## 使用方法

### 1. 使用装饰器定义任务

```python
from api.core.rq_app import job

@job(queue='default', timeout=300, result_ttl=3600, description="示例任务")
def example_task(name: str, delay: int = 5):
    """示例任务"""
    # 任务逻辑
    return f"任务 {name} 完成"

# 提交任务
job_instance = example_task.queue("测试任务", 10)
print(f"任务ID: {job_instance.id}")
```

### 2. 使用函数方式提交任务

```python
from api.core.rq_app import get_queue

def my_function(param1, param2):
    return param1 + param2

# 获取队列并提交任务
queue = get_queue("default")
job = queue.enqueue(my_function, "hello", "world")
```

### 3. 不同优先级队列

```python
# 高优先级任务
@job(queue='high', timeout=600)
def urgent_task(data):
    # 紧急任务逻辑
    pass

# 低优先级任务
@job(queue='low', timeout=1800)
def background_task(items):
    # 后台任务逻辑
    pass
```

### 4. 任务状态查询

```python
from api.core.rq_app import get_job

# 通过任务ID获取任务
job = get_job("task_id_here")

if job:
    print(f"状态: {job.get_status()}")
    print(f"结果: {job.result}")
    print(f"是否完成: {job.is_finished}")
    print(f"是否失败: {job.is_failed}")
```

## API 端点

新增了 Flask-RQ2 演示 API，可以通过以下端点测试功能：

### 基础端点

- `GET /api/flask-rq2/demo/info` - 获取演示信息
- `GET /api/flask-rq2/queues/info` - 获取队列信息

### 任务提交

- `POST /api/flask-rq2/example-task` - 提交示例任务
- `POST /api/flask-rq2/high-priority-task` - 提交高优先级任务
- `POST /api/flask-rq2/batch-task` - 提交批处理任务
- `POST /api/flask-rq2/submit-all-examples` - 提交所有示例任务

### 任务管理

- `GET /api/flask-rq2/task/{task_id}/status` - 获取任务状态
- `DELETE /api/flask-rq2/task/{task_id}` - 取消任务

## 示例请求

### 提交示例任务

```bash
curl -X POST "http://localhost:8000/api/flask-rq2/example-task" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试任务",
    "delay": 5
  }'
```

### 提交高优先级任务

```bash
curl -X POST "http://localhost:8000/api/flask-rq2/high-priority-task" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "type": "urgent",
      "value": 100
    }
  }'
```

### 查询任务状态

```bash
curl "http://localhost:8000/api/flask-rq2/task/{task_id}/status"
```

## 测试

运行测试脚本验证配置：

```bash
python test_flask_rq2.py
```

测试脚本会验证：
- Flask-RQ2 导入
- Redis 连接
- 队列创建
- 简单任务执行
- 装饰器任务执行
- 队列信息查询

## 启动 Worker

使用现有的 RQ Worker 启动脚本：

```bash
python start_rq_worker.py
```

或者使用 VS Code 任务：
- `Ctrl+Shift+P` -> `Tasks: Run Task` -> `启动RQ Worker`

## 监控

### RQ Dashboard

启动 RQ Dashboard 监控界面：

```bash
python start_rq_dashboard.py
```

访问: http://localhost:9181

### 队列信息 API

通过 API 获取队列信息：

```bash
curl "http://localhost:8000/api/flask-rq2/queues/info"
```

## 迁移指南

### 从原生 RQ 迁移

1. **现有任务函数**: 无需修改，继续使用 `get_queue().enqueue()`
2. **新任务**: 推荐使用 `@job` 装饰器
3. **配置**: 已自动迁移到 Flask-RQ2

### 装饰器参数

```python
@job(
    queue='default',        # 队列名称
    timeout=300,           # 任务超时时间（秒）
    result_ttl=3600,       # 结果保留时间（秒）
    ttl=None,              # 任务生存时间
    failure_ttl=None,      # 失败任务保留时间
    depends_on=None,       # 依赖的任务
    job_id=None,           # 自定义任务ID
    at_front=False,        # 是否插入到队列前端
    meta=None,             # 元数据
    description=None,      # 任务描述
    retry=None             # 重试配置
)
```

## 注意事项

1. **兼容性**: 现有的 RQ 代码继续工作，无需立即迁移
2. **性能**: Flask-RQ2 提供了更好的性能和功能
3. **监控**: 可以同时使用 RQ Dashboard 和新的 API 端点进行监控
4. **Worker**: 使用相同的 RQ Worker，无需更改

## 故障排除

### 常见问题

1. **导入错误**: 确保已安装 `flask_rq2`
   ```bash
   pip install flask_rq2
   ```

2. **Redis 连接失败**: 检查 Redis 配置和连接
3. **任务不执行**: 确保 RQ Worker 正在运行
4. **装饰器错误**: 检查装饰器参数是否正确

### 调试

启用调试日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

查看任务详细信息：

```python
from api.core.rq_app import get_job

job = get_job("task_id")
print(f"任务信息: {job.__dict__}")
```
