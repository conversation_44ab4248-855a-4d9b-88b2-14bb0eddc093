.chat-page-container {
  display: flex;
  flex-direction: column;
  height: 100%; // Fill parent height instead of viewport height
 
  // Removed padding: 20px;
  background-color: #f0f2f5; 

  .chat-card {
    flex: 1;
    margin: 20px; // Add margin for spacing around the card
    display: flex;
    flex-direction: column;
    overflow: hidden; // Prevent content overflow
    border-radius: 8px; // Rounded corners for the card
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09); // Subtle shadow
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 200px); // 设置最大高度，确保滚动条显示
    padding: 16px 24px; // Padding inside the message list
    background-color: #ffffff; // White background for messages
    position: relative; // 确保定位上下文
    overscroll-behavior: contain; // 防止滚动传播
    -webkit-overflow-scrolling: touch; // 在iOS上提供平滑滚动

    .empty-chat {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #888;
      font-size: 16px;
    }

    .message-item {
      margin-bottom: 16px;
      padding: 0 !important; // Override default antd padding if needed

      &.user {
        .ant-list-item-meta {
          align-items: flex-start; // Align avatar top
        }
        .ant-list-item-meta-content {
          display: flex;
          flex-direction: column;
          align-items: flex-end; // Align user message content to the right
        }
        .ant-list-item-meta-avatar {
          order: 1; // Move avatar to the right for user messages
          margin-left: 12px;
          margin-right: 0;
        }
        .message-content-wrapper {
          background-color: #e6f7ff; // Light blue background for user messages
          border-radius: 10px 10px 0 10px;
          padding: 8px 12px;
          max-width: 100%;
          box-sizing: border-box;
          // w/wdwrap: break-word; // RRomed d ffmm rappp
          text-align: left; 
        }
        .a-nsag--wontent-wrappmage-content { // T { //aTarget content rget content inside user wrapper
           word-break: break-word;   // Allow brrbkinr l:eg -o ds if/necAlloryng words if necessary
        }
        .ant-list-item-meta-title {
          text-align: right; // Align title right
          margin-bottom: 4px;
          color: #555;
          font-size: 12px;
        }
      }

      &.assistant {
        .ant-list-item-meta {
          align-items: flex-start; // Align avatar top
        }
        .ant-list-item-meta-avatar {
          margin-right: 12px;
        }
        .message-content-wrapper {
          background-color: #f5f5f5; // Light grey background for assistant messages
          border-radius: 10px 10px 10px 0;
          padding: 8px 12px;
          max-width: 70%;
          word-wrap: break-word; // Ensure long words break
        }
         .ant-list-item-meta-title {
          margin-bottom: 4px;
          color: #555;
          font-size: 12px;
        }
      }

      .message-content {
        white-space: pre-wrap; // Preserve whitespace and wrap text
        font-size: 14px;
        line-height: 1.5;

        // Basic Markdown Styling
        h1, h2, h3, h4, h5, h6 {
          margin-top: 1em;
          margin-bottom: 0.5em;
          font-weight: 600;
        }
        p {
          margin-bottom: 0.8em;
        }
        ul, ol {
          margin-left: 20px;
          margin-bottom: 0.8em;
        }
        li {
          margin-bottom: 0.2em;
        }
        code {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 0.2em 0.4em;
          border-radius: 3px;
          font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
          font-size: 0.9em;
        }
        pre {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto; // Allow horizontal scrolling for long code lines
          margin-bottom: 0.8em;
          code {
            background-color: transparent; // Reset background for code inside pre
            padding: 0;
          }
        }
        blockquote {
          border-left: 4px solid #ccc;
          padding-left: 10px;
          color: #666;
          margin-left: 0;
          margin-right: 0;
          margin-bottom: 0.8em;
        }
        table {
          border-collapse: collapse;
          margin-bottom: 1em;
          width: auto; // Adjust table width as needed
        }
        th, td {
          border: 1px solid #ddd;
          padding: 6px 10px;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
      }
    }
  }

  .input-container {
    padding: 16px 24px;
    background: #ffffff;
    border-top: 1px solid #f0f0f0;
    position: relative; // Needed for absolute positioning of the button

    .ant-input-textarea-wrapper {
      flex: 1;
      // margin-right: 16px; // Removed as button is now absolute
      textarea {
         border-radius: 6px; // Rounded corners for textarea
         padding-right: 80px; // Add padding to prevent text overlap with button
      }
    }

    .send-button {
       position: absolute; // Position absolutely within input-container
       right: 34px; // Adjust right position (considering container padding)
       bottom: 26px; // Adjust bottom position (considering container padding)
       border-radius: 6px; 
       // height: auto; // Keep default or adjust as needed
       // min-height: 32px; 
       padding: 4px 15px; 
       // align-self: flex-end; // Not needed for absolute positioning
       // margin-bottom: 4px; // Not needed for absolute positioning
    }
  }
}
