"""
测试任务模块
用于测试Flask-RQ2功能的任务函数
"""

import time

def simple_test_task(name):
    """
    简单测试任务

    Args:
        name: 名称

    Returns:
        问候消息
    """
    return f"Hello, {name}!"

def decorated_test_task(message):
    """
    装饰器测试任务

    Args:
        message: 消息

    Returns:
        处理后的消息
    """
    return f"装饰器任务: {message}"

def high_priority_test_task(data):
    """
    高优先级测试任务

    Args:
        data: 数据

    Returns:
        处理结果
    """
    time.sleep(1)  # 模拟处理时间
    return {
        "status": "completed",
        "data": data,
        "processed_at": time.time()
    }
