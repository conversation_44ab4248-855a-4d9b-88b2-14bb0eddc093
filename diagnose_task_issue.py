#!/usr/bin/env python3
"""
任务管理问题诊断脚本
用于诊断为什么RQ Dashboard能看到任务，但任务管理页面看不到任务的问题
"""

import redis
import json
from rq import Queue
from rq.job import Job
from rq.registry import StartedJobRegistry, FinishedJobRegistry, FailedJobRegistry
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_redis_connection():
    """获取Redis连接"""
    try:
        return redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return None

def check_rq_queues():
    """检查RQ队列中的任务"""
    print("🔍 检查RQ队列中的任务...")

    try:
        redis_conn = redis.Redis(host='localhost', port=6379, db=0)

        # 检查默认队列
        queue = Queue('default', connection=redis_conn)

        print(f"📋 默认队列状态:")
        print(f"  - 队列长度: {len(queue)}")
        print(f"  - 等待任务: {queue.count}")

        # 获取队列中的任务
        jobs = queue.get_jobs()
        print(f"  - 队列中的任务: {len(jobs)}")

        for job in jobs[:5]:  # 只显示前5个任务
            print(f"    * 任务ID: {job.id}")
            print(f"      状态: {job.get_status()}")
            print(f"      函数: {job.func_name}")
            print(f"      创建时间: {job.created_at}")
            print()

        # 检查正在执行的任务
        started_registry = StartedJobRegistry(queue=queue)
        started_jobs = started_registry.get_job_ids()
        print(f"  - 正在执行的任务: {len(started_jobs)}")

        for job_id in started_jobs[:3]:
            job = Job.fetch(job_id, connection=redis_conn)
            print(f"    * 任务ID: {job_id}")
            print(f"      状态: {job.get_status()}")
            print(f"      函数: {job.func_name}")
            print()

        # 检查已完成的任务
        finished_registry = FinishedJobRegistry(queue=queue)
        finished_jobs = finished_registry.get_job_ids()
        print(f"  - 已完成的任务: {len(finished_jobs)}")

        # 检查失败的任务
        failed_registry = FailedJobRegistry(queue=queue)
        failed_jobs = failed_registry.get_job_ids()
        print(f"  - 失败的任务: {len(failed_jobs)}")

    except Exception as e:
        print(f"❌ 检查RQ队列失败: {e}")

def check_task_monitor_redis():
    """检查任务监控器在Redis中的数据"""
    print("\n🔍 检查任务监控器Redis数据...")

    redis_client = get_redis_connection()
    if not redis_client:
        return

    try:
        # 检查活跃任务
        active_tasks_key = "task_monitor:active_tasks"
        active_task_ids = redis_client.smembers(active_tasks_key)
        print(f"📋 任务监控器活跃任务: {len(active_task_ids)}")

        for task_id in list(active_task_ids)[:5]:
            print(f"  - 任务ID: {task_id}")

            # 获取任务详情
            task_details_key = f"task_monitor:task_details:{task_id}"
            task_details_json = redis_client.get(task_details_key)

            if task_details_json:
                task_details = json.loads(task_details_json)
                print(f"    状态: {task_details.get('status', 'Unknown')}")
                print(f"    名称: {task_details.get('task_name', 'Unknown')}")
                print(f"    创建时间: {task_details.get('created_at', 'Unknown')}")
            else:
                print(f"    ❌ 找不到任务详情")
            print()

        # 检查已完成任务
        completed_tasks_key = "task_monitor:completed_tasks"
        completed_task_count = redis_client.zcard(completed_tasks_key)
        print(f"📋 任务监控器已完成任务: {completed_task_count}")

        completed_task_ids = redis_client.zrevrange(completed_tasks_key, 0, 4)
        for task_id in completed_task_ids:
            print(f"  - 任务ID: {task_id}")

            # 获取任务详情
            task_details_key = f"task_monitor:task_details:{task_id}"
            task_details_json = redis_client.get(task_details_key)

            if task_details_json:
                task_details = json.loads(task_details_json)
                print(f"    状态: {task_details.get('status', 'Unknown')}")
                print(f"    名称: {task_details.get('task_name', 'Unknown')}")
            print()

    except Exception as e:
        print(f"❌ 检查任务监控器Redis数据失败: {e}")

def check_api_endpoints():
    """检查API端点是否正常"""
    print("\n🔍 检查API端点...")

    try:
        import requests

        base_url = "http://localhost:8000"

        # 检查活跃任务API
        try:
            response = requests.get(f"{base_url}/tasks/active", timeout=5)
            print(f"📡 /tasks/active API:")
            print(f"  - 状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"  - 返回任务数: {len(data)}")

                for task in data[:3]:
                    print(f"    * 任务ID: {task.get('task_id', 'Unknown')}")
                    print(f"      状态: {task.get('status', 'Unknown')}")
                    print(f"      名称: {task.get('task_name', 'Unknown')}")
            else:
                print(f"  - 错误: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"  - ❌ 请求失败: {e}")

        # 检查已完成任务API
        try:
            response = requests.get(f"{base_url}/tasks/completed?limit=5", timeout=5)
            print(f"\n📡 /tasks/completed API:")
            print(f"  - 状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"  - 返回任务数: {len(data)}")
            else:
                print(f"  - 错误: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"  - ❌ 请求失败: {e}")

    except ImportError:
        print("  - ⚠️ requests库未安装，跳过API检查")

def sync_rq_tasks_to_monitor():
    """同步RQ任务到任务监控器"""
    print("\n🔄 尝试同步RQ任务到任务监控器...")

    redis_client = get_redis_connection()
    if not redis_client:
        return

    try:
        redis_conn = redis.Redis(host='localhost', port=6379, db=0)

        queue = Queue('default', connection=redis_conn)

        # 获取所有任务
        all_jobs = []

        # 队列中的任务
        all_jobs.extend(queue.get_jobs())

        # 正在执行的任务
        started_registry = StartedJobRegistry(queue=queue)
        for job_id in started_registry.get_job_ids():
            try:
                job = Job.fetch(job_id, connection=redis_conn)
                all_jobs.append(job)
            except:
                pass

        # 已完成的任务
        finished_registry = FinishedJobRegistry(queue=queue)
        for job_id in finished_registry.get_job_ids()[:10]:  # 只同步最近10个
            try:
                job = Job.fetch(job_id, connection=redis_conn)
                all_jobs.append(job)
            except:
                pass

        print(f"📋 找到 {len(all_jobs)} 个RQ任务")

        # 同步到任务监控器
        synced_count = 0
        for job in all_jobs:
            try:
                task_id = job.id
                task_name = job.func_name or "unknown_task"
                status = job.get_status()

                # 映射状态
                status_mapping = {
                    "queued": "PENDING",
                    "started": "STARTED",
                    "finished": "SUCCESS",
                    "failed": "FAILURE",
                    "deferred": "PENDING",
                    "scheduled": "PENDING",
                    "canceled": "REVOKED"
                }

                mapped_status = status_mapping.get(status, status.upper())

                # 检查是否已存在
                task_details_key = f"task_monitor:task_details:{task_id}"
                existing = redis_client.get(task_details_key)

                if not existing:
                    # 创建任务详情
                    task_details = {
                        "task_id": task_id,
                        "task_name": task_name,
                        "status": mapped_status,
                        "created_at": int(job.created_at.timestamp()) if job.created_at else 0,
                        "synced": True  # 标记为同步的任务
                    }

                    if job.result is not None:
                        task_details["result"] = str(job.result)

                    if job.exc_info:
                        task_details["error"] = str(job.exc_info)

                    # 保存任务详情
                    redis_client.set(
                        task_details_key,
                        json.dumps(task_details),
                        ex=86400  # 24小时过期
                    )

                    # 添加到相应的集合
                    if mapped_status in ["SUCCESS", "FAILURE", "REVOKED"]:
                        redis_client.zadd("task_monitor:completed_tasks", {task_id: task_details["created_at"]})
                    else:
                        redis_client.sadd("task_monitor:active_tasks", task_id)

                    synced_count += 1
                    print(f"  ✅ 同步任务: {task_id} ({task_name}) - {mapped_status}")

            except Exception as e:
                print(f"  ❌ 同步任务失败 {job.id}: {e}")

        print(f"\n🎉 成功同步 {synced_count} 个任务到任务监控器")

    except Exception as e:
        print(f"❌ 同步任务失败: {e}")

def main():
    """主函数"""
    print("🚀 任务管理问题诊断开始...\n")

    # 检查Redis连接
    redis_client = get_redis_connection()
    if not redis_client:
        print("❌ 无法连接到Redis，请确保Redis服务正在运行")
        return

    print("✅ Redis连接正常\n")

    # 检查RQ队列
    check_rq_queues()

    # 检查任务监控器数据
    check_task_monitor_redis()

    # 检查API端点
    check_api_endpoints()

    # 询问是否同步任务
    print("\n" + "="*50)
    response = input("是否要同步RQ任务到任务监控器？(y/N): ").strip().lower()

    if response in ['y', 'yes']:
        sync_rq_tasks_to_monitor()
        print("\n🔄 同步完成，请刷新任务管理页面查看结果")

    print("\n🎉 诊断完成！")

if __name__ == "__main__":
    main()
