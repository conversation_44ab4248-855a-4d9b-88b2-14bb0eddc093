.price-chat-page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.price-chat-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: rgba(0, 0, 0, 0.45);
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  width: 100%;
}

.message-item.user {
  justify-content: flex-end;
  flex-direction: row;
}

.message-item.assistant {
  justify-content: flex-start;
  flex-direction: row;
}

.avatar-left {
  margin-right: 12px;
  flex-shrink: 0;
}

.avatar-right {
  margin-left: 12px;
  flex-shrink: 0;
}

.message-content-wrapper {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 8px;
  display: inline-flex;
  flex-direction: column;
  gap: 8px;
}

.message-item.user .message-content-wrapper {
  background-color: #e6f7ff;
  margin-left: 0;
  margin-right: 12px;
  border-top-right-radius: 2px;
}

.message-item.assistant .message-content-wrapper {
  background-color: #f5f5f5;
  margin-left: 12px;
  margin-right: 0;
  border-top-left-radius: 2px;
}

.message-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

.input-container {
  border-top: 1px solid #f0f0f0;
  padding: 16px;
  background-color: #fff;
  position: relative;
}

.chat-input {
  flex: 1;
  margin-bottom: 0;
  padding-right: 90px;
}

.send-button {
  position: absolute;
  right: 32px;
  bottom: 32px;
  width: auto;
  height: 40px;
}
