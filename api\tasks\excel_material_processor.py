"""
Excel材料处理任务
支持分批异步处理Excel数据，查询材料价格，并保存结果
"""

import os
import time
import json
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from sqlalchemy.orm import Session

from ..core.task_manager import job, submit_task, get_task_status
from ..core.debug import get_app_logger
from ..core.database import SessionLocal
from ..models.item import Materials
from ..crud.item import get_items
from ..config import UPLOAD_DIR

# 获取日志记录器
logger = get_app_logger("excel_material_processor")

# 文件保存配置
EXCEL_STORAGE_DIR = os.path.join(UPLOAD_DIR, "excel_materials")
RESULT_STORAGE_DIR = os.path.join(UPLOAD_DIR, "excel_results")
DEFAULT_RETENTION_DAYS = 30

# 确保目录存在
os.makedirs(EXCEL_STORAGE_DIR, exist_ok=True)
os.makedirs(RESULT_STORAGE_DIR, exist_ok=True)

class ExcelMaterialProcessor:
    """Excel材料处理器"""

    def __init__(self):
        self.batch_size = 50  # 每批处理的行数
        self.api_delay = 0.1  # API请求间隔（秒）

    def validate_excel_headers(self, headers: List[str]) -> Dict[str, str]:
        """
        验证Excel标题行并返回字段映射

        Args:
            headers: Excel标题行

        Returns:
            字段映射字典
        """
        required_fields = ["名称", "型号", "规格", "数量", "价格", "备注"]
        field_mapping = {}

        # 标准字段映射
        standard_mapping = {
            "名称": "product_name",
            "型号": "specification",
            "规格": "specification",
            "数量": "quantity",
            "价格": "unit_price",
            "备注": "remarks",
            "品牌": "brand",
            "产地": "origin",
            "单位": "unit",
            "金额": "amount"
        }

        # 模糊匹配标题
        for header in headers:
            header_clean = str(header).strip()
            for key, db_field in standard_mapping.items():
                if key in header_clean:
                    field_mapping[header_clean] = db_field
                    break

        # 检查必需字段
        mapped_fields = set(field_mapping.values())
        missing_fields = []
        for field in ["product_name", "quantity"]:  # 最基本的必需字段
            if field not in mapped_fields:
                missing_fields.append(field)

        if missing_fields:
            logger.warning(f"缺少必需字段: {missing_fields}")

        return field_mapping

    def process_excel_row(self, row_data: Dict[str, Any], field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        处理单行Excel数据

        Args:
            row_data: 原始行数据
            field_mapping: 字段映射

        Returns:
            处理后的数据
        """
        processed_data = {}

        for excel_field, value in row_data.items():
            if excel_field in field_mapping:
                db_field = field_mapping[excel_field]

                # 数据类型转换和清理
                if pd.isna(value) or value == "":
                    processed_data[db_field] = None
                elif db_field in ["quantity", "unit_price", "amount"]:
                    # 数值字段处理
                    try:
                        processed_data[db_field] = float(value) if value is not None else None
                    except (ValueError, TypeError):
                        processed_data[db_field] = None
                        logger.warning(f"无法转换数值字段 {db_field}: {value}")
                else:
                    # 文本字段处理
                    processed_data[db_field] = str(value).strip() if value is not None else None

        return processed_data

    async def query_material_price_api(self, material_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        调用API查询材料价格信息

        Args:
            material_data: 材料数据

        Returns:
            查询结果
        """
        try:
            # 模拟API调用延迟
            await asyncio.sleep(self.api_delay)

            # 这里应该调用实际的价格查询API
            # 目前返回模拟数据
            return {
                "api_id": f"api_{uuid.uuid4().hex[:8]}",
                "found": True,
                "suggested_price": material_data.get("unit_price"),
                "market_price": None,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"API查询失败: {e}")
            return {
                "api_id": None,
                "found": False,
                "error": str(e)
            }

    def query_database_material(self, material_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从数据库查询材料信息

        Args:
            material_data: 材料数据

        Returns:
            数据库查询结果
        """
        try:
            db = SessionLocal()

            # 构建查询条件
            query = db.query(Materials)

            if material_data.get("product_name"):
                query = query.filter(Materials.product_name.ilike(f"%{material_data['product_name']}%"))

            if material_data.get("specification"):
                query = query.filter(Materials.specification.ilike(f"%{material_data['specification']}%"))

            if material_data.get("brand"):
                query = query.filter(Materials.brand.ilike(f"%{material_data['brand']}%"))

            # 获取最匹配的记录
            materials = query.limit(5).all()

            if materials:
                best_match = materials[0]
                return {
                    "db_id": best_match.id,
                    "product_name": best_match.product_name,
                    "specification": best_match.specification,
                    "brand": best_match.brand,
                    "unit_price": best_match.unit_price,
                    "found": True
                }
            else:
                return {"found": False}

        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            return {"found": False, "error": str(e)}
        finally:
            db.close()

    def calculate_final_price(self, original_data: Dict[str, Any],
                            api_result: Dict[str, Any],
                            db_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算最终价格

        Args:
            original_data: 原始数据
            api_result: API查询结果
            db_result: 数据库查询结果

        Returns:
            最终价格信息
        """
        final_price = original_data.get("unit_price")
        price_source = "original"

        # 价格优先级：API > 数据库 > 原始
        if api_result.get("found") and api_result.get("suggested_price"):
            final_price = api_result["suggested_price"]
            price_source = "api"
        elif db_result.get("found") and db_result.get("unit_price"):
            final_price = db_result["unit_price"]
            price_source = "database"

        # 计算金额
        quantity = original_data.get("quantity", 0)
        final_amount = (final_price * quantity) if final_price and quantity else None

        return {
            "final_price": final_price,
            "final_amount": final_amount,
            "price_source": price_source,
            "price_updated": price_source != "original"
        }

@job(queue='default', timeout=1800, result_ttl=86400*30, description="Excel材料批处理任务")
def process_excel_batch(batch_data: List[Dict[str, Any]],
                       field_mapping: Dict[str, str],
                       batch_index: int,
                       total_batches: int,
                       task_id: str) -> Dict[str, Any]:
    """
    处理Excel数据批次

    Args:
        batch_data: 批次数据
        field_mapping: 字段映射
        batch_index: 批次索引
        total_batches: 总批次数
        task_id: 主任务ID

    Returns:
        批次处理结果
    """
    logger.info(f"开始处理批次 {batch_index + 1}/{total_batches}，数据量: {len(batch_data)}")

    processor = ExcelMaterialProcessor()
    processed_results = []

    for i, row_data in enumerate(batch_data):
        try:
            # 处理行数据
            processed_data = processor.process_excel_row(row_data, field_mapping)

            # 异步查询API（这里简化为同步）
            api_result = asyncio.run(processor.query_material_price_api(processed_data))

            # 查询数据库
            db_result = processor.query_database_material(processed_data)

            # 计算最终价格
            price_info = processor.calculate_final_price(processed_data, api_result, db_result)

            # 合并结果
            final_result = {
                **processed_data,
                **price_info,
                "api_result": api_result,
                "db_result": db_result,
                "row_index": batch_index * processor.batch_size + i,
                "processed_at": datetime.now().isoformat()
            }

            processed_results.append(final_result)

        except Exception as e:
            logger.error(f"处理行数据失败 (批次 {batch_index + 1}, 行 {i + 1}): {e}")
            processed_results.append({
                "error": str(e),
                "row_index": batch_index * processor.batch_size + i,
                "original_data": row_data
            })

    batch_result = {
        "batch_index": batch_index,
        "total_batches": total_batches,
        "processed_count": len(processed_results),
        "success_count": len([r for r in processed_results if "error" not in r]),
        "error_count": len([r for r in processed_results if "error" in r]),
        "results": processed_results,
        "completed_at": datetime.now().isoformat()
    }

    logger.info(f"批次 {batch_index + 1} 处理完成，成功: {batch_result['success_count']}, 失败: {batch_result['error_count']}")

    return batch_result

@job(queue='high', timeout=3600, result_ttl=86400*30, description="Excel材料主处理任务")
def process_excel_materials(file_path: str,
                          headers: List[str],
                          data: List[Dict[str, Any]],
                          metadata: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理Excel材料数据主任务

    Args:
        file_path: 文件路径
        headers: 标题行
        data: Excel数据
        metadata: 元数据

    Returns:
        处理结果
    """
    task_id = f"excel_material_{uuid.uuid4().hex[:8]}"
    logger.info(f"开始处理Excel材料任务 {task_id}，数据行数: {len(data)}")

    try:
        processor = ExcelMaterialProcessor()

        # 验证标题并生成字段映射
        field_mapping = processor.validate_excel_headers(headers)
        logger.info(f"字段映射: {field_mapping}")

        # 分批处理数据
        batch_size = processor.batch_size
        total_batches = (len(data) + batch_size - 1) // batch_size
        batch_tasks = []

        # 提交批处理任务
        for i in range(0, len(data), batch_size):
            batch_data = data[i:i + batch_size]
            batch_index = i // batch_size

            batch_task_id = submit_task(
                process_excel_batch,
                batch_data,
                field_mapping,
                batch_index,
                total_batches,
                task_id,
                queue="default",
                description=f"Excel批处理 {batch_index + 1}/{total_batches}"
            )

            batch_tasks.append({
                "batch_index": batch_index,
                "task_id": batch_task_id,
                "data_count": len(batch_data)
            })

        # 保存任务信息
        task_info = {
            "task_id": task_id,
            "file_path": file_path,
            "total_rows": len(data),
            "total_batches": total_batches,
            "batch_tasks": batch_tasks,
            "field_mapping": field_mapping,
            "metadata": metadata,
            "created_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(days=DEFAULT_RETENTION_DAYS)).isoformat(),
            "status": "processing"
        }

        # 保存任务信息到文件
        task_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}.json")
        with open(task_file, 'w', encoding='utf-8') as f:
            json.dump(task_info, f, ensure_ascii=False, indent=2)

        logger.info(f"Excel材料处理任务 {task_id} 已启动，共 {total_batches} 个批次")

        return {
            "task_id": task_id,
            "status": "processing",
            "total_rows": len(data),
            "total_batches": total_batches,
            "batch_tasks": batch_tasks,
            "message": f"已启动 {total_batches} 个批处理任务"
        }

    except Exception as e:
        logger.error(f"Excel材料处理任务失败: {e}", exc_info=True)
        return {
            "task_id": task_id,
            "status": "failed",
            "error": str(e),
            "failed_at": datetime.now().isoformat()
        }

@job(queue='low', timeout=600, result_ttl=86400*30, description="Excel结果合并任务")
def merge_excel_results(task_id: str) -> Dict[str, Any]:
    """
    合并Excel处理结果

    Args:
        task_id: 主任务ID

    Returns:
        合并后的结果
    """
    logger.info(f"开始合并Excel处理结果: {task_id}")

    try:
        # 读取任务信息
        task_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}.json")
        if not os.path.exists(task_file):
            raise FileNotFoundError(f"任务文件不存在: {task_id}")

        with open(task_file, 'r', encoding='utf-8') as f:
            task_info = json.load(f)

        batch_tasks = task_info.get("batch_tasks", [])
        all_results = []
        completed_batches = 0
        failed_batches = 0

        # 收集所有批次结果
        for batch_task in batch_tasks:
            batch_task_id = batch_task["task_id"]
            batch_status = get_task_status(batch_task_id)

            if batch_status["status"] == "finished":
                batch_result = batch_status.get("result", {})
                if "results" in batch_result:
                    all_results.extend(batch_result["results"])
                completed_batches += 1
            elif batch_status["status"] == "failed":
                failed_batches += 1
                logger.error(f"批次任务失败: {batch_task_id}")

        # 统计信息
        total_processed = len(all_results)
        success_count = len([r for r in all_results if "error" not in r])
        error_count = len([r for r in all_results if "error" in r])

        # 价格更新统计
        price_updated_count = len([r for r in all_results if r.get("price_updated")])

        # 生成最终结果文件
        final_result = {
            "task_id": task_id,
            "status": "completed" if failed_batches == 0 else "partial_success",
            "summary": {
                "total_rows": task_info.get("total_rows", 0),
                "total_batches": task_info.get("total_batches", 0),
                "completed_batches": completed_batches,
                "failed_batches": failed_batches,
                "total_processed": total_processed,
                "success_count": success_count,
                "error_count": error_count,
                "price_updated_count": price_updated_count
            },
            "results": all_results,
            "completed_at": datetime.now().isoformat(),
            "expires_at": task_info.get("expires_at")
        }

        # 保存最终结果
        result_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}_final.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2)

        # 生成Excel结果文件
        excel_result_file = generate_excel_result(task_id, all_results, task_info)
        final_result["excel_file"] = excel_result_file

        # 更新任务状态
        task_info["status"] = final_result["status"]
        task_info["completed_at"] = final_result["completed_at"]
        task_info["summary"] = final_result["summary"]

        with open(task_file, 'w', encoding='utf-8') as f:
            json.dump(task_info, f, ensure_ascii=False, indent=2)

        logger.info(f"Excel结果合并完成: {task_id}, 成功: {success_count}, 失败: {error_count}")

        return final_result

    except Exception as e:
        logger.error(f"合并Excel结果失败: {e}", exc_info=True)
        return {
            "task_id": task_id,
            "status": "merge_failed",
            "error": str(e),
            "failed_at": datetime.now().isoformat()
        }

def generate_excel_result(task_id: str, results: List[Dict[str, Any]], task_info: Dict[str, Any]) -> str:
    """
    生成Excel结果文件

    Args:
        task_id: 任务ID
        results: 处理结果
        task_info: 任务信息

    Returns:
        Excel文件路径
    """
    try:
        # 准备Excel数据
        excel_data = []
        for result in results:
            if "error" not in result:
                row = {
                    "行号": result.get("row_index", ""),
                    "产品名称": result.get("product_name", ""),
                    "规格型号": result.get("specification", ""),
                    "品牌": result.get("brand", ""),
                    "数量": result.get("quantity", ""),
                    "原始价格": result.get("unit_price", ""),
                    "最终价格": result.get("final_price", ""),
                    "最终金额": result.get("final_amount", ""),
                    "价格来源": result.get("price_source", ""),
                    "价格已更新": "是" if result.get("price_updated") else "否",
                    "备注": result.get("remarks", ""),
                    "处理时间": result.get("processed_at", "")
                }
                excel_data.append(row)

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 生成Excel文件
        excel_filename = f"{task_id}_result.xlsx"
        excel_path = os.path.join(RESULT_STORAGE_DIR, excel_filename)

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 写入处理结果
            df.to_excel(writer, sheet_name='处理结果', index=False)

            # 写入统计信息
            summary_data = [
                ["任务ID", task_id],
                ["原始文件", task_info.get("metadata", {}).get("filename", "")],
                ["总行数", task_info.get("total_rows", 0)],
                ["处理成功", len([r for r in results if "error" not in r])],
                ["处理失败", len([r for r in results if "error" in r])],
                ["价格更新", len([r for r in results if r.get("price_updated")])],
                ["处理时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                ["过期时间", task_info.get("expires_at", "")]
            ]

            summary_df = pd.DataFrame(summary_data, columns=["项目", "值"])
            summary_df.to_excel(writer, sheet_name='统计信息', index=False)

        logger.info(f"Excel结果文件已生成: {excel_path}")
        return excel_filename

    except Exception as e:
        logger.error(f"生成Excel结果文件失败: {e}")
        return ""

def check_excel_task_status(task_id: str) -> Dict[str, Any]:
    """
    检查Excel处理任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    try:
        task_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}.json")
        if not os.path.exists(task_file):
            return {"status": "not_found", "message": "任务不存在"}

        with open(task_file, 'r', encoding='utf-8') as f:
            task_info = json.load(f)

        # 检查批次任务状态
        batch_tasks = task_info.get("batch_tasks", [])
        batch_status = {}
        completed_count = 0
        failed_count = 0

        for batch_task in batch_tasks:
            batch_task_id = batch_task["task_id"]
            status = get_task_status(batch_task_id)
            batch_status[batch_task_id] = status["status"]

            if status["status"] == "finished":
                completed_count += 1
            elif status["status"] == "failed":
                failed_count += 1

        total_batches = len(batch_tasks)
        progress = (completed_count / total_batches * 100) if total_batches > 0 else 0

        # 更新任务状态
        if completed_count == total_batches:
            task_info["status"] = "ready_for_merge"
        elif failed_count > 0 and completed_count + failed_count == total_batches:
            task_info["status"] = "partial_complete"

        return {
            "task_id": task_id,
            "status": task_info.get("status", "unknown"),
            "progress": progress,
            "total_batches": total_batches,
            "completed_batches": completed_count,
            "failed_batches": failed_count,
            "batch_status": batch_status,
            "created_at": task_info.get("created_at"),
            "expires_at": task_info.get("expires_at"),
            "summary": task_info.get("summary")
        }

    except Exception as e:
        logger.error(f"检查任务状态失败: {e}")
        return {"status": "error", "message": str(e)}

def cleanup_expired_tasks():
    """清理过期的任务文件"""
    try:
        current_time = datetime.now()
        cleaned_count = 0

        for filename in os.listdir(RESULT_STORAGE_DIR):
            if filename.endswith('.json'):
                file_path = os.path.join(RESULT_STORAGE_DIR, filename)

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        task_data = json.load(f)

                    expires_at = task_data.get("expires_at")
                    if expires_at:
                        expire_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                        if current_time > expire_time:
                            # 删除相关文件
                            os.remove(file_path)

                            # 删除Excel结果文件
                            task_id = task_data.get("task_id", "")
                            excel_file = os.path.join(RESULT_STORAGE_DIR, f"{task_id}_result.xlsx")
                            if os.path.exists(excel_file):
                                os.remove(excel_file)

                            cleaned_count += 1
                            logger.info(f"清理过期任务: {task_id}")

                except Exception as e:
                    logger.error(f"清理文件失败 {filename}: {e}")

        logger.info(f"清理完成，删除 {cleaned_count} 个过期任务")
        return cleaned_count

    except Exception as e:
        logger.error(f"清理过期任务失败: {e}")
        return 0
