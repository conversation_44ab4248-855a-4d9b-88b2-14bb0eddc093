# 数据同步服务模块

import time
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import update # 添加 update
from ..core.database import SessionLocal, get_db
from ..core.vector_db import get_vector_db_client
from ..models.item import Materials
from ..core.logging_config import get_logger

logger = get_logger("sync_service")

class SyncService:
    """同步服务，负责将数据从PostgreSQL同步到向量数据库"""

    def __init__(self):
        self.vector_db_client = get_vector_db_client()
        self.last_sync_time = None
        self.is_syncing = False
        self.sync_lock = threading.Lock()  # 用于防止并发同步
        self.batch_size = 100 # 批量处理大小

    def sync_all_data(self) -> Dict[str, Any]:
        """同步所有数据到向量数据库"""
        # 使用锁防止并发同步
        if not self.sync_lock.acquire(blocking=False):
            logger.warning("已有同步任务正在进行，跳过本次全量同步")
            return {"status": "skipped", "reason": "已有同步任务正在进行"}

        try:
            self.is_syncing = True
            logger.info("开始全量同步（仅处理未同步）数据到向量数据库...")
            db = SessionLocal()
            result = self._perform_sync(db)
            return result
        except Exception as e:
            logger.error(f"全量同步数据时发生意外错误: {e}")
            return {"status": "error", "error": str(e)}
        finally:
            if db:
                db.close()
            self.is_syncing = False
            self.sync_lock.release()

    def sync_pending_data(self) -> Dict[str, Any]:
        """同步所有标记为未同步的数据到向量数据库"""
        if not self.sync_lock.acquire(blocking=False):
            logger.warning("已有同步任务正在进行，跳过本次待处理同步")
            return {"status": "skipped", "reason": "已有同步任务正在进行"}

        try:
            self.is_syncing = True
            logger.info("开始同步待处理（未同步）数据到向量数据库...")
            db = SessionLocal()
            result = self._perform_sync(db)
            return result
        except Exception as e:
            logger.error(f"同步待处理数据时发生意外错误: {e}")
            return {"status": "error", "error": str(e)}
        finally:
            if db:
                db.close()
            self.is_syncing = False
            self.sync_lock.release()

    def _perform_sync(self, db: Session) -> Dict[str, Any]:
        """执行实际的同步操作：查询未同步数据，上传，更新状态"""
        start_time = time.time()
        success_count = 0
        error_count = 0
        processed_count = 0
        ids_to_update = []

        try:
            # 查询所有未同步的记录
            materials_to_sync = db.query(Materials).filter(Materials.is_synced_to_milvus == False).all()
            total_count = len(materials_to_sync)
            logger.info(f"共找到 {total_count} 条未同步记录需要处理")

            if total_count == 0:
                elapsed_time = time.time() - start_time
                sync_time = datetime.now()
                self.last_sync_time = sync_time
                logger.info("没有需要同步的数据")
                return {
                    "status": "success", "message": "没有需要同步的数据",
                    "total": 0, "success": 0, "error": 0,
                    "elapsed_time": f"{elapsed_time:.2f}秒",
                    "sync_time": sync_time.strftime("%Y-%m-%d %H:%M:%S")
                }

            for material in materials_to_sync:
                processed_count += 1
                item_data = {
                    "id": material.id,
                    "product_name": material.product_name,
                    "contract_id": material.contract_id,
                    "contract_number": material.contract_number,
                    "specification": material.specification,
                    "brand": material.brand,
                    "origin": material.origin,
                    "quantity": material.quantity,
                    "unit": material.unit,
                    "unit_price": str(material.unit_price), # 确保与vector_db兼容
                    "amount": str(material.amount), # 确保与vector_db兼容
                    "warranty_months": material.warranty_months,
                    "includes_delivery_installation": material.includes_delivery_installation,
                    # 注意：vector_db.upsert_item内部会处理嵌入生成
                }

                # 同步到向量数据库
                try:
                    # 使用线程安全的方式调用异步方法
                    import asyncio
                    try:
                        # 为每个线程创建新的事件循环
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        # 在事件循环中执行异步操作
                        result = loop.run_until_complete(
                            self.vector_db_client.upsert_item(item_data)
                        )
                        # 关闭事件循环
                        loop.close()

                        if result is None:
                            error_count += 1
                            logger.warning(f"同步 ID {material.id} 失败: 返回None")
                        elif result:
                            success_count += 1
                            ids_to_update.append(material.id)
                        else:
                            error_count += 1
                            logger.warning(f"同步 ID {material.id} 失败")
                    except asyncio.TimeoutError:
                        error_count += 1
                        logger.error(f"同步 ID {material.id} 超时")
                    except Exception as e:
                        error_count += 1
                        logger.error(f"同步 ID {material.id} 时发生错误: {e}")
                        logger.debug(f"同步错误详情: {str(e)}", exc_info=True)
                except Exception as e:
                    error_count += 1
                    logger.error(f"同步 ID {material.id} 时发生错误: {e}")
                    # 记录详细错误信息以便调试
                    logger.debug(f"同步错误详情: {str(e)}", exc_info=True)
                    # 如果异步操作失败，确保不更新数据库状态
                    continue

                # 批量更新数据库状态
                if len(ids_to_update) >= self.batch_size or processed_count == total_count:
                    if ids_to_update:
                        try:
                            # 使用 SQLAlchemy Core API 进行批量更新
                            stmt = (
                                update(Materials)
                                .where(Materials.id.in_(ids_to_update))
                                .values(is_synced_to_milvus=True)
                            )
                            db.execute(stmt)
                            db.commit()
                            logger.info(f"批量更新 {len(ids_to_update)} 条记录的同步状态成功")
                            ids_to_update = [] # 清空列表
                        except Exception as update_err:
                            db.rollback()
                            logger.error(f"批量更新同步状态失败: {update_err}")
                            # 注意：这里没有将对应的 success_count 减去，因为向量库插入可能已成功
                            # 可以考虑更复杂的错误处理逻辑，例如记录失败的ID稍后重试

                # 每N条记录输出一次进度
                if processed_count % self.batch_size == 0:
                    logger.info(f"已处理 {processed_count}/{total_count} 条记录...")

            # 确保最后剩余的也提交
            if ids_to_update:
                try:
                    stmt = (
                        update(Materials)
                        .where(Materials.id.in_(ids_to_update))
                        .values(is_synced_to_milvus=True)
                    )
                    db.execute(stmt)
                    db.commit()
                    logger.info(f"批量更新最后 {len(ids_to_update)} 条记录的同步状态成功")
                except Exception as update_err:
                    db.rollback()
                    logger.error(f"批量更新最后同步状态失败: {update_err}")

            # 更新最后同步时间
            sync_time = datetime.now()
            self.last_sync_time = sync_time
            elapsed_time = time.time() - start_time

            result = {
                "status": "success",
                "total": total_count,
                "success": success_count, # 成功插入向量库的数量
                "error": error_count,   # 插入向量库失败的数量
                "elapsed_time": f"{elapsed_time:.2f}秒",
                "sync_time": sync_time.strftime("%Y-%m-%d %H:%M:%S")
            }
            logger.info(f"同步完成: 总计处理{total_count}条，成功同步{success_count}条，失败{error_count}条，耗时{elapsed_time:.2f}秒")
            return result

        except Exception as e:
            db.rollback() # 出错时回滚
            logger.error(f"执行同步操作时出错: {e}")
            # 可能需要更详细的错误报告
            return {"status": "error", "error": str(e), "total": total_count, "success": success_count, "error": error_count}


    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            "is_syncing": self.is_syncing,
            "last_sync_time": self.last_sync_time.strftime("%Y-%m-%d %H:%M:%S") if self.last_sync_time else None
        }

# 单例模式，确保只创建一个同步服务实例
_sync_service = None
_sync_scheduler = None

def get_sync_service() -> SyncService:
    """获取同步服务实例"""
    global _sync_service
    if _sync_service is None:
        _sync_service = SyncService()
    return _sync_service

def start_sync_scheduler(interval_minutes: int = 30):
    """启动定时同步任务"""
    global _sync_scheduler
    if _sync_scheduler is not None:
        return

    import schedule
    import threading

    # 使用schedule库创建定时任务
    def run_schedule():
        while True:
            schedule.run_pending()
            time.sleep(1)

    # 创建定时任务
    schedule.every(interval_minutes).minutes.do(get_sync_service().sync_pending_data)

    # 在后台线程中运行调度器
    _sync_scheduler = threading.Thread(target=run_schedule, daemon=True)
    _sync_scheduler.start()

    logger.info(f"已启动定时同步任务，间隔 {interval_minutes} 分钟")

def stop_sync_scheduler():
    """停止定时同步任务"""
    global _sync_scheduler
    if _sync_scheduler:
        import schedule
        # 清除所有定时任务
        schedule.clear()
        # 线程是daemon=True，会随主线程退出而退出
        _sync_scheduler = None
        logger.info("已停止定时同步任务")
