import React, { useState } from 'react';
import { Button, message, Card, Descriptions } from 'antd';
import { SyncOutlined } from '@ant-design/icons';
import { manualSync } from '../../api/sync';

const SyncPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState<any>(null);

  const handleSync = async () => {
    setLoading(true);
    try {
      const result = await manualSync();
      setSyncStatus(result);
      if (result.status === 'success') {
        message.success('同步成功');
      } else {
        message.warning(result.message || '同步完成但有部分失败');
      }
    } catch (error) {
      message.error('同步失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="向量数据同步" bordered={false}>
        <Button
          type="primary"
          icon={<SyncOutlined />}
          loading={loading}
          onClick={handleSync}
        >
          立即同步到向量库
        </Button>

        {syncStatus && (
          <Descriptions bordered style={{ marginTop: 16 }}>
            <Descriptions.Item label="状态">{syncStatus.status}</Descriptions.Item>
            <Descriptions.Item label="总记录数">{syncStatus.total || 0}</Descriptions.Item>
            <Descriptions.Item label="成功数">{syncStatus.success || 0}</Descriptions.Item>
            <Descriptions.Item label="失败数">{syncStatus.error || 0}</Descriptions.Item>
            <Descriptions.Item label="耗时">{syncStatus.elapsed_time}</Descriptions.Item>
            <Descriptions.Item label="同步时间">{syncStatus.sync_time}</Descriptions.Item>
          </Descriptions>
        )}
      </Card>
    </div>
  );
};

export default SyncPage;
