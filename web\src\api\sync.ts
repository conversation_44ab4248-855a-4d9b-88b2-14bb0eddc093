import request from '../utils/request';

export async function manualSync() {
  const response = await request.post('/sync/run');
  if (response.data?.status === 'started') {
    // 轮询获取同步状态
    return await pollSyncStatus();
  }
  return response;
}

async function pollSyncStatus(): Promise<any> {
  const maxAttempts = 30;
  const interval = 2000; // 2秒
  
  for (let i = 0; i < maxAttempts; i++) {
    const status = await request.get('/sync/status');
    if (status.data?.status !== 'syncing') {
      return status;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  return {
    status: 'timeout',
    message: '同步状态轮询超时'
  };
}
