import { Upload, Button, Table, message, Steps, Form, Select, Card, Typography } from 'antd';
import { UploadOutlined, InboxOutlined, SolutionOutlined, LoadingOutlined, CheckCircleOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';

const { Step } = Steps;
const { Option } = Select;

interface PreviewData {
  possible_headers: Array<{ row_index: number; content: any[] }>;
  headers: string[];
  field_mapping: Record<string, string>;
  available_fields: string[];
  preview_data: any[];
  selected_header_row: number;
  all_data?: any[];
  end_row: number;
  end_data: any[];
}

export default function ImportPage() {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);
  const [previewData, setPreviewData] = useState<PreviewData>({
    possible_headers: [],
    headers: [],
    field_mapping: {},
    available_fields: [],
    preview_data: [],
    selected_header_row: 0,
    all_data: [],
    end_row: 0,
    end_data: []
  });
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const steps = [
    { title: '上传文件', icon: <UploadOutlined /> },
    { title: '配置映射', icon: <SolutionOutlined /> },
    { title: '完成导入', icon: <LoadingOutlined /> },
  ];

  const FIELD_MAPPINGS = {
    product_name: ['产品名称', '设备名称', '物料名称', '名称'],
    specification: ['规格型号', '规格', '型号'],
    description: ['产品描述', '描述', '说明'],
    manufacturer: ['品牌', '厂家', '生产厂家', '制造商'],
    origin: ['产地', '原产地'],
    quantity: ['数量'],
    unit: ['单位'],
    unit_price: ['单价', '价格'],
    total_price: ['金额', '总价', '合计'],
    remark: ['备注', '注释'],
    includes_delivery_installation: ['送货安装', '包含安装', '送装服务', '安装服务', '是否送装'],
    warranty_months: ['保修月份', '保修期', '质保期', '质量保修', '保修时间']
  };

  const handlePreview = async () => {
    if (!fileList.length) {
      message.error('请先选择文件');
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', fileList[0]);
    formData.append('header_row', String(previewData.selected_header_row));
    try {
      const response = await fetch('/api/upload/preview', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
        },
        body: formData,
      });
      
      if (!response.ok) throw new Error('预览失败');
      
      const data = await response.json();
      const allData = data.all_data || data.preview_data;
      
      const initialMapping: Record<string, string> = {};
      data.headers?.forEach((header: string) => {
        const field = Object.entries(FIELD_MAPPINGS)
          .find(([_, names]) => names.includes(header))?.[0] || '';
        initialMapping[header] = field;
      });
      
      setPreviewData({
        ...data,
        all_data: allData
      });
      setMapping(initialMapping);
      setCurrentStep(1);
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', fileList[0]);
      
      // 将数字转为字符串，但在 URL 中会被正确解析为数字
      formData.append('header_row', `${previewData.selected_header_row}`);
      formData.append('end_row', `${previewData.end_row}`);
      formData.append('field_mapping', JSON.stringify(mapping));

      const response = await fetch('/api/upload/', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail?.error || '导入失败');
      }
      
      const result = await response.json();
      message.success(result.message || '导入成功');
      setCurrentStep(2);
    } catch (error) {
      message.error(error.message || '导入过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  const updatePreviewDataForHeaderRow = (headerRowIndex: number) => {
    if (!previewData.all_data || previewData.all_data.length === 0) return;
    
    const headerRow = previewData.possible_headers.find(h => h.row_index === headerRowIndex);
    if (!headerRow) return;
    
    const headers = headerRow.content;
    
    const newPreviewData: Record<string, string>[] = [];
    if (previewData.all_data && headerRowIndex + 1 < previewData.all_data.length) {
      const rowData: Record<string, string> = {};
      headers.forEach((header, index) => {
        if (previewData.all_data && previewData.all_data[headerRowIndex + 1] && 
            index < previewData.all_data[headerRowIndex + 1].length) {
          rowData[header] = previewData.all_data[headerRowIndex + 1][index] as string;
        } else {
          rowData[header] = '';
        }
      });
      newPreviewData.push(rowData);
    }
    
    setPreviewData(prev => ({
      ...prev,
      headers: headers,
      preview_data: newPreviewData,
      selected_header_row: headerRowIndex
    }));
    
    const newMapping: Record<string, string> = {};
    headers.forEach((header) => {
      const field = Object.entries(FIELD_MAPPINGS)
        .find(([_, names]) => names.includes(header))?.[0] || '';
      newMapping[header] = field;
    });
    setMapping(newMapping);
  };

  const renderMappingForm = () => (
    <Form form={form} layout="vertical">
      <Form.Item label="选择标题行">
        <Select
          value={previewData.selected_header_row}
          onChange={(value: number) => {
            form.setFieldsValue({ header_row: value });
            updatePreviewDataForHeaderRow(value);
          }}
        >
          {previewData?.possible_headers?.map(header => (
            <Option key={header.row_index} value={header.row_index}>
              第{header.row_index + 1}行: {header.content.join(', ').substring(0, 50)}...
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Typography.Title level={5}>字段映射配置</Typography.Title>
      <Table
        dataSource={previewData?.headers?.map(header => ({
          header,
          sample: previewData.preview_data[previewData.selected_header_row+1],
          mapping: mapping[header],
        }))}
        pagination={false}
      >
        <Table.Column title="Excel列名" dataIndex="header" />
        <Table.Column title="示例数据" dataIndex="sample" />
        <Table.Column
          title="映射字段"
          render={(_, record: any) => (
            <Select
              value={Object.entries(FIELD_MAPPINGS)
                .find(([field]) => field === record.mapping)?.[1][0] || ''}
              onChange={(value: string) => {
                const field = Object.entries(FIELD_MAPPINGS)
                  .find(([_, names]) => names.includes(value))?.[0] || '';
                setMapping({ ...mapping, [record.header]: field });
              }}
              style={{ width: 150 }}
              showSearch
              optionFilterProp="children"
              filterOption={(input: string, option: any) =>
                (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {Object.entries(FIELD_MAPPINGS).map(([field, names]) => (
                <Option key={field} value={names[0]}>
                  {names[0]}
                </Option>
              ))}
              <Option value="">不导入</Option>
            </Select>
          )}
        />
      </Table>
      
      <Typography.Text type="secondary" style={{ display: 'block', marginTop: 16 }}>
        注：当前显示的示例数据为标题行（第{previewData.selected_header_row + 1}行）的下一行数据
      </Typography.Text>

      <Form.Item label="结束行预览">
        <Select
          value={previewData.end_row}
          onChange={(value: number) => {
            setPreviewData(prev => ({
              ...prev,
              end_row: value
            }));
          }}
          style={{ width: '100%' }}
        >
          {previewData.end_data.map((row, index) => (
            <Option key={index} value={index}>
              第{index + 1}行: {Object.values(row).filter(v => v).join(' ')}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Typography.Text type="secondary" style={{ display: 'block', marginTop: 8 }}>
        选中的结束行数据：{Object.values(previewData.end_data[previewData.end_row] || {}).filter(v => v).join(' ')}
      </Typography.Text>
    </Form>
  );

  return (
    <Card title="数据导入向导" style={{ margin: 16 }}>
      <Steps current={currentStep} style={{ marginBottom: 32 }}>
        {steps.map((item, index) => (
          <Step key={index} title={item.title} icon={item.icon} />
        ))}
      </Steps>

      {currentStep === 0 && (
        <Upload.Dragger
          accept=".xls,.xlsx"
          fileList={fileList}
          beforeUpload={(file: any) => {
            setFileList([file]);
            return false;
          }}
          maxCount={1}
        >
          <p className="ant-upload-drag-icon"><InboxOutlined /></p>
          <p className="ant-upload-text">点击或拖拽文件上传</p>
        </Upload.Dragger>
      )}

      {currentStep === 1 && previewData && renderMappingForm()}

      {currentStep === 2 && (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Typography.Title level={4} style={{ color: '#52c41a' }}>
            <CheckCircleOutlined /> 导入成功
          </Typography.Title>
          <Typography.Paragraph>
            您的数据已成功导入系统
          </Typography.Paragraph>
        </div>
      )}

      <div style={{ marginTop: 24, textAlign: 'right' }}>
        {currentStep === 0 && (
          <Button 
            type="primary" 
            onClick={handlePreview}
            disabled={!fileList.length}
            loading={loading}
          >
            下一步
          </Button>
        )}

        {currentStep === 1 && (
          <>
            <Button style={{ marginRight: 8 }} onClick={() => setCurrentStep(0)}>
              上一步
            </Button>
            <Button 
              type="primary" 
              onClick={handleImport}
              loading={loading}
            >
              开始导入
            </Button>
          </>
        )}

        {currentStep === 2 && (
          <Button 
            type="primary" 
            onClick={() => {
              setFileList([]);
              setPreviewData({
                possible_headers: [],
                headers: [],
                field_mapping: {},
                available_fields: [],
                preview_data: [],
                selected_header_row: 0,
                all_data: [],
                end_row: 0,
                end_data: []
              });
              setMapping({});
              setCurrentStep(0);
            }}
          >
            再次导入
          </Button>
        )}
      </div>
    </Card>
  );
}
