{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "strict": true, "jsx": "preserve", "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["vite/client"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules"]}