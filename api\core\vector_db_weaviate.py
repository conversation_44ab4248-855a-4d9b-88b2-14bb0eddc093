# Weaviate向量数据库操作模块

from typing import List, Dict, Any, Optional
from ..config import WEAVIATE_CLASS_NAME
from ..core.logging_config import get_logger
from .vector_embedding import VectorEmbedding

logger = get_logger("vector_db_weaviate")

class WeaviateClient:
    """Weaviate数据库操作类"""

    def __init__(self, client):
        self.client = client

    async def upsert_item(self, item_data: dict) -> bool:
        """向Weaviate中插入或更新一条记录"""
        try:
            item_id = item_data.get("id")
            if item_id is None:
                logger.error("Upsert Weaviate 失败：缺少 'id' 字段")
                return False

            # 准备用于生成嵌入的文本
            text_parts = []
            for key, value in item_data.items():
                if key in ['product_name', 'specification', 'brand'] and value is not None:
                    text_parts.append(f"{str(value).strip()}")
            combined_text = ' '.join(text_parts)

            if not combined_text:
                logger.warning(f"ID {item_id} 的可嵌入文本为空，跳过插入")
                return False

            # 生成向量（Weaviate目前仅支持稠密向量）
            embedding_result = await VectorEmbedding.generate_hybrid_embedding(combined_text)
            if not embedding_result:
                logger.error(f"为 ID {item_id} 生成向量失败")
                return False

            # 准备数据对象
            data_object = {
                "product_name": item_data.get("product_name", ""),
                "specification": item_data.get("specification", ""),
                "brand": item_data.get("brand", ""),
                "origin": item_data.get("origin", ""),
                "original_id": item_id
            }

            # 插入数据
            self.client.data_object.create(
                data_object,
                WEAVIATE_CLASS_NAME,
                vector=embedding_result['dense']
            )
            logger.debug(f"已向Weaviate插入/更新ID为 {item_id} 的记录")
            return True

        except Exception as e:
            logger.error(f"向Weaviate插入/更新记录 ID {item_id} 时出错: {e}")
            return False

    async def delete_item(self, item_id: int) -> bool:
        """从Weaviate中删除一条记录"""
        try:
            # 构建查询以找到对应的UUID
            query = {
                "class": WEAVIATE_CLASS_NAME,
                "where": {
                    "path": ["original_id"],
                    "operator": "Equal",
                    "valueNumber": item_id
                }
            }
            result = self.client.query.get(WEAVIATE_CLASS_NAME, ["_additional {id}"]).with_where(query).do()
            
            # 检查是否找到对象
            if result and "data" in result and "Get" in result["data"]:
                objects = result["data"]["Get"][WEAVIATE_CLASS_NAME]
                if objects and len(objects) > 0:
                    uuid = objects[0]["_additional"]["id"]
                    self.client.data_object.delete(uuid, WEAVIATE_CLASS_NAME)
                    logger.debug(f"已从Weaviate删除ID为 {item_id} 的记录")
                    return True

            logger.warning(f"未找到ID为 {item_id} 的记录")
            return False

        except Exception as e:
            logger.error(f"从Weaviate删除记录 ID {item_id} 时出错: {e}")
            return False

    async def search_items(self, query_text: str, limit: int = 10, threshold: float = None, sparse_weight: float = 0.5, dense_weight: float = 0.5) -> List[Dict[str, Any]]:
        """在Weaviate中执行向量搜索
        
        Args:
            query_text: 查询文本
            limit: 返回结果数量限制
            threshold: 相似度阈值
            sparse_weight: 稀疏向量权重（Weaviate不支持，将被忽略）
            dense_weight: 稠密向量权重（Weaviate不支持，将被忽略）
        """
        try:
            # 记录权重参数被忽略的信息
            if sparse_weight != 0.5 or dense_weight != 0.5:
                logger.warning("Weaviate目前不支持向量权重设置，sparse_weight和dense_weight参数将被忽略")

            # 生成查询向量
            embedding_result = await VectorEmbedding.generate_hybrid_embedding(query_text)
            if not embedding_result:
                logger.error("生成查询向量失败")
                return []

            # 构建查询
            query = self.client.query.get(
                WEAVIATE_CLASS_NAME,
                ["product_name", "specification", "brand", "origin", "original_id", "_additional {certainty}"]
            ).with_near_vector({
                "vector": embedding_result['dense']
            }).with_limit(limit)

            # 执行查询
            result = query.do()

            # 处理结果
            search_results = []
            if result and "data" in result and "Get" in result["data"]:
                for item in result["data"]["Get"][WEAVIATE_CLASS_NAME]:
                    certainty = item["_additional"]["certainty"]
                    if threshold is None or certainty >= threshold:
                        search_results.append({
                            "id": item["original_id"],
                            "product_name": item["product_name"],
                            "specification": item["specification"],
                            "brand": item["brand"],
                            "origin": item["origin"],
                            "score": certainty
                        })

            logger.info(f"Weaviate搜索完成，找到{len(search_results)}条结果")
            return search_results

        except Exception as e:
            logger.error(f"执行Weaviate搜索时出错: {e}")
            return []