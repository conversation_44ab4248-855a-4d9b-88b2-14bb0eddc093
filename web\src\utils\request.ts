import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';

const instance = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器
instance.interceptors.request.use((config) => {
  return config;
}, (error) => {
  return Promise.reject(error);
});

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    if (response.status === 200) {
      return response.data;
    }
    message.error(response.data?.message || '请求失败');
    return Promise.reject(response.data);
  },
  (error) => {
    message.error(error.response?.data?.message || '网络错误');
    return Promise.reject(error);
  }
);

export default instance;
