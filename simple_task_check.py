#!/usr/bin/env python3
"""
简单的任务检查脚本
检查RQ任务和任务监控器的状态
"""

import redis
import json
import requests

def check_redis_connection():
    """检查Redis连接"""
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Redis连接正常")
        return r
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return None

def check_task_monitor_data(redis_client):
    """检查任务监控器数据"""
    print("\n🔍 检查任务监控器数据...")
    
    try:
        # 检查活跃任务
        active_tasks = redis_client.smembers("task_monitor:active_tasks")
        print(f"📋 活跃任务数量: {len(active_tasks)}")
        
        for task_id in list(active_tasks)[:3]:
            task_key = f"task_monitor:task_details:{task_id}"
            task_data = redis_client.get(task_key)
            if task_data:
                task_info = json.loads(task_data)
                print(f"  - {task_id}: {task_info.get('status')} ({task_info.get('task_name')})")
        
        # 检查已完成任务
        completed_count = redis_client.zcard("task_monitor:completed_tasks")
        print(f"📋 已完成任务数量: {completed_count}")
        
        completed_tasks = redis_client.zrevrange("task_monitor:completed_tasks", 0, 2)
        for task_id in completed_tasks:
            task_key = f"task_monitor:task_details:{task_id}"
            task_data = redis_client.get(task_key)
            if task_data:
                task_info = json.loads(task_data)
                print(f"  - {task_id}: {task_info.get('status')} ({task_info.get('task_name')})")
                
    except Exception as e:
        print(f"❌ 检查任务监控器数据失败: {e}")

def check_api_endpoints():
    """检查API端点"""
    print("\n🔍 检查API端点...")
    
    try:
        # 检查活跃任务API
        response = requests.get("http://localhost:8000/tasks/active", timeout=5)
        print(f"📡 /tasks/active: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回 {len(data)} 个活跃任务")
            for task in data[:3]:
                print(f"  - {task.get('task_id', 'Unknown')}: {task.get('status', 'Unknown')}")
        else:
            print(f"  错误: {response.text}")
            
        # 检查已完成任务API
        response = requests.get("http://localhost:8000/tasks/completed?limit=5", timeout=5)
        print(f"📡 /tasks/completed: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回 {len(data)} 个已完成任务")
        else:
            print(f"  错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")

def check_rq_keys(redis_client):
    """检查RQ相关的Redis键"""
    print("\n🔍 检查RQ Redis键...")
    
    try:
        # 查找所有RQ相关的键
        rq_keys = redis_client.keys("rq:*")
        print(f"📋 找到 {len(rq_keys)} 个RQ键")
        
        # 分类显示
        queue_keys = [k for k in rq_keys if ":queue:" in k]
        job_keys = [k for k in rq_keys if ":job:" in k]
        registry_keys = [k for k in rq_keys if "registry" in k]
        
        print(f"  - 队列键: {len(queue_keys)}")
        print(f"  - 任务键: {len(job_keys)}")
        print(f"  - 注册表键: {len(registry_keys)}")
        
        # 显示一些具体的键
        if queue_keys:
            print("  队列键示例:")
            for key in queue_keys[:3]:
                print(f"    {key}")
                
        if registry_keys:
            print("  注册表键示例:")
            for key in registry_keys[:3]:
                print(f"    {key}")
                
    except Exception as e:
        print(f"❌ 检查RQ键失败: {e}")

def sync_rq_to_monitor(redis_client):
    """简单的同步RQ任务到监控器"""
    print("\n🔄 尝试同步RQ任务到监控器...")
    
    try:
        # 查找所有RQ任务键
        job_keys = redis_client.keys("rq:job:*")
        print(f"📋 找到 {len(job_keys)} 个RQ任务")
        
        synced_count = 0
        for job_key in job_keys[:10]:  # 只处理前10个
            try:
                # 提取任务ID
                task_id = job_key.split(":")[-1]
                
                # 获取任务数据
                job_data = redis_client.hgetall(job_key)
                if not job_data:
                    continue
                
                # 检查是否已存在于监控器
                monitor_key = f"task_monitor:task_details:{task_id}"
                if redis_client.exists(monitor_key):
                    continue
                
                # 创建监控器任务记录
                status = job_data.get('status', 'unknown')
                func_name = job_data.get('description', 'unknown_task')
                
                # 状态映射
                status_mapping = {
                    'queued': 'PENDING',
                    'started': 'STARTED',
                    'finished': 'SUCCESS',
                    'failed': 'FAILURE',
                    'deferred': 'PENDING'
                }
                
                mapped_status = status_mapping.get(status, status.upper())
                
                task_details = {
                    "task_id": task_id,
                    "task_name": func_name,
                    "status": mapped_status,
                    "created_at": int(job_data.get('created_at', '0')),
                    "synced": True
                }
                
                # 保存到监控器
                redis_client.set(
                    monitor_key,
                    json.dumps(task_details),
                    ex=86400
                )
                
                # 添加到相应集合
                if mapped_status in ["SUCCESS", "FAILURE", "REVOKED"]:
                    redis_client.zadd("task_monitor:completed_tasks", {task_id: task_details["created_at"]})
                else:
                    redis_client.sadd("task_monitor:active_tasks", task_id)
                
                synced_count += 1
                print(f"  ✅ 同步: {task_id} - {mapped_status}")
                
            except Exception as e:
                print(f"  ❌ 同步失败 {job_key}: {e}")
        
        print(f"\n🎉 成功同步 {synced_count} 个任务")
        
    except Exception as e:
        print(f"❌ 同步失败: {e}")

def main():
    """主函数"""
    print("🚀 任务状态检查开始...\n")
    
    # 检查Redis连接
    redis_client = check_redis_connection()
    if not redis_client:
        return
    
    # 检查任务监控器数据
    check_task_monitor_data(redis_client)
    
    # 检查RQ键
    check_rq_keys(redis_client)
    
    # 检查API端点
    check_api_endpoints()
    
    # 询问是否同步
    print("\n" + "="*50)
    response = input("是否要同步RQ任务到任务监控器？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        sync_rq_to_monitor(redis_client)
        print("\n🔄 同步完成，请刷新任务管理页面")
    
    print("\n🎉 检查完成！")

if __name__ == "__main__":
    main()
