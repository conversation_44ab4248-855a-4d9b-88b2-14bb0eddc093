"""
Celery应用配置模块
用于配置和初始化Celery应用实例
"""

from celery import Celery
from ..config import CELERY_BROKER_URL, CELERY_RESULT_BACKEND

# 创建Celery应用实例
celery_app = Celery(
    "price_db_tasks",  # 应用名称
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=["api.tasks.sync_tasks"]  # 包含的任务模块
)

# Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=False,
    worker_max_tasks_per_child=1000,  # 每个worker处理的最大任务数
    worker_prefetch_multiplier=4,  # worker预取任务数量
    task_acks_late=True,  # 任务执行完成后再确认
    task_track_started=True,  # 跟踪任务开始状态
)

# 定义Celery定时任务
celery_app.conf.beat_schedule = {
    'sync-materials-every-day': {
        'task': 'api.tasks.sync_tasks.sync_materials',
        'schedule': 86400.0,  # 每24小时执行一次
        'args': (),
    },
}

# 导出Celery应用实例
def get_celery_app():
    return celery_app
