#!/usr/bin/env python
"""
Excel材料处理功能测试脚本
"""

import sys
import os
import time
import json
import pandas as pd
from io import BytesIO

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_excel():
    """创建测试Excel文件"""
    print("创建测试Excel文件...")
    
    # 测试数据
    test_data = [
        ["名称", "型号", "规格", "数量", "价格", "备注"],
        ["电缆", "YJV-3*120", "3芯120平方", 100, 85.5, "阻燃电缆"],
        ["开关", "DZ47-63", "63A单极", 50, 12.8, "小型断路器"],
        ["插座", "86型", "五孔插座", 200, 8.5, "墙壁插座"],
        ["灯具", "LED-40W", "40W面板灯", 80, 45.0, "办公照明"],
        ["管材", "PVC-25", "25mm PVC管", 500, 3.2, "电线管"],
        ["配电箱", "PZ30-12", "12回路配电箱", 10, 180.0, "家用配电箱"],
        ["电线", "BV-2.5", "2.5平方单芯线", 1000, 2.8, "铜芯电线"],
        ["接线盒", "86型", "标准接线盒", 150, 4.5, "暗装接线盒"],
        ["空开", "DZ47LE-32", "32A漏电保护器", 30, 68.0, "漏电保护"],
        ["电表", "DDS102", "单相电能表", 20, 120.0, "智能电表"]
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_data[1:], columns=test_data[0])
    
    # 保存为Excel文件
    test_file = "test_materials.xlsx"
    df.to_excel(test_file, index=False)
    
    print(f"✓ 测试Excel文件已创建: {test_file}")
    print(f"  - 数据行数: {len(df)}")
    print(f"  - 列名: {list(df.columns)}")
    
    return test_file

def test_excel_processor_import():
    """测试Excel处理器导入"""
    print("测试Excel处理器导入...")
    
    try:
        from api.tasks.excel_material_processor import (
            ExcelMaterialProcessor, process_excel_materials, 
            process_excel_batch, merge_excel_results
        )
        print("✓ Excel处理器导入成功")
        return True
    except Exception as e:
        print(f"✗ Excel处理器导入失败: {e}")
        return False

def test_excel_validation():
    """测试Excel验证功能"""
    print("测试Excel验证功能...")
    
    try:
        from api.tasks.excel_material_processor import ExcelMaterialProcessor
        
        processor = ExcelMaterialProcessor()
        
        # 测试标题验证
        headers = ["名称", "型号", "规格", "数量", "价格", "备注"]
        field_mapping = processor.validate_excel_headers(headers)
        
        print(f"✓ 标题验证成功")
        print(f"  - 字段映射: {field_mapping}")
        
        # 测试数据处理
        test_row = {
            "名称": "测试产品",
            "型号": "TEST-001", 
            "数量": "10",
            "价格": "25.5",
            "备注": "测试备注"
        }
        
        processed_data = processor.process_excel_row(test_row, field_mapping)
        print(f"✓ 数据处理成功")
        print(f"  - 处理结果: {processed_data}")
        
        return True
        
    except Exception as e:
        print(f"✗ Excel验证测试失败: {e}")
        return False

def test_database_query():
    """测试数据库查询功能"""
    print("测试数据库查询功能...")
    
    try:
        from api.tasks.excel_material_processor import ExcelMaterialProcessor
        
        processor = ExcelMaterialProcessor()
        
        # 测试数据库查询
        test_material = {
            "product_name": "电缆",
            "specification": "YJV",
            "brand": None
        }
        
        db_result = processor.query_database_material(test_material)
        print(f"✓ 数据库查询成功")
        print(f"  - 查询结果: {db_result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库查询测试失败: {e}")
        return False

def test_api_query():
    """测试API查询功能"""
    print("测试API查询功能...")
    
    try:
        import asyncio
        from api.tasks.excel_material_processor import ExcelMaterialProcessor
        
        processor = ExcelMaterialProcessor()
        
        # 测试API查询
        test_material = {
            "product_name": "电缆",
            "unit_price": 85.5
        }
        
        api_result = asyncio.run(processor.query_material_price_api(test_material))
        print(f"✓ API查询成功")
        print(f"  - 查询结果: {api_result}")
        
        return True
        
    except Exception as e:
        print(f"✗ API查询测试失败: {e}")
        return False

def test_price_calculation():
    """测试价格计算功能"""
    print("测试价格计算功能...")
    
    try:
        from api.tasks.excel_material_processor import ExcelMaterialProcessor
        
        processor = ExcelMaterialProcessor()
        
        # 测试价格计算
        original_data = {"unit_price": 10.0, "quantity": 5}
        api_result = {"found": True, "suggested_price": 12.0}
        db_result = {"found": True, "unit_price": 11.0}
        
        price_info = processor.calculate_final_price(original_data, api_result, db_result)
        print(f"✓ 价格计算成功")
        print(f"  - 计算结果: {price_info}")
        
        return True
        
    except Exception as e:
        print(f"✗ 价格计算测试失败: {e}")
        return False

def test_task_submission():
    """测试任务提交功能"""
    print("测试任务提交功能...")
    
    try:
        from api.core.task_manager import submit_task
        from api.tasks.excel_material_processor import process_excel_batch
        
        # 准备测试数据
        test_batch = [
            {"名称": "测试产品1", "数量": "10", "价格": "25.0"},
            {"名称": "测试产品2", "数量": "5", "价格": "30.0"}
        ]
        
        field_mapping = {
            "名称": "product_name",
            "数量": "quantity", 
            "价格": "unit_price"
        }
        
        # 提交批处理任务
        task_id = submit_task(
            process_excel_batch,
            test_batch,
            field_mapping,
            0,  # batch_index
            1,  # total_batches
            "test_task",
            queue="default",
            description="测试批处理任务"
        )
        
        print(f"✓ 任务提交成功")
        print(f"  - 任务ID: {task_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ 任务提交测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作功能"""
    print("测试文件操作功能...")
    
    try:
        from api.tasks.excel_material_processor import (
            EXCEL_STORAGE_DIR, RESULT_STORAGE_DIR
        )
        
        # 检查目录是否存在
        print(f"✓ Excel存储目录: {EXCEL_STORAGE_DIR}")
        print(f"  - 存在: {os.path.exists(EXCEL_STORAGE_DIR)}")
        
        print(f"✓ 结果存储目录: {RESULT_STORAGE_DIR}")
        print(f"  - 存在: {os.path.exists(RESULT_STORAGE_DIR)}")
        
        # 创建目录（如果不存在）
        os.makedirs(EXCEL_STORAGE_DIR, exist_ok=True)
        os.makedirs(RESULT_STORAGE_DIR, exist_ok=True)
        
        print("✓ 文件操作测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 文件操作测试失败: {e}")
        return False

def test_task_status_check():
    """测试任务状态检查功能"""
    print("测试任务状态检查功能...")
    
    try:
        from api.tasks.excel_material_processor import check_excel_task_status
        
        # 测试不存在的任务
        status = check_excel_task_status("non_existent_task")
        print(f"✓ 任务状态检查成功")
        print(f"  - 不存在任务状态: {status['status']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 任务状态检查测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Excel材料处理功能测试")
    print("=" * 60)
    
    # 创建测试Excel文件
    test_file = create_test_excel()
    
    tests = [
        test_excel_processor_import,
        test_excel_validation,
        test_database_query,
        test_api_query,
        test_price_calculation,
        test_task_submission,
        test_file_operations,
        test_task_status_check
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        try:
            if test():
                passed += 1
            else:
                print("  测试未通过")
        except Exception as e:
            print(f"  测试异常: {e}")
        print("-" * 40)
    
    print()
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！Excel材料处理功能正常。")
        print()
        print("使用说明:")
        print("1. 启动应用: uvicorn api.main:app --reload")
        print("2. 启动Worker: python start_rq_worker.py")
        print("3. 访问API: http://localhost:8000/api/excel-material/info")
        print(f"4. 上传测试文件: {test_file}")
        return True
    else:
        print("✗ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
