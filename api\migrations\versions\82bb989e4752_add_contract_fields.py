"""add contract fields

Revision ID: 82bb989e4752
Revises: 71c336260f87
Create Date: 2025-04-11 17:59:43.724596

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '82bb989e4752'
down_revision: Union[str, None] = '71c336260f87'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_materials_id', table_name='materials')
    op.drop_index('ix_materials_is_synced_to_milvus', table_name='materials')
    op.drop_index('ix_materials_product_name', table_name='materials')
    op.drop_table('materials')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('materials',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='产品名称'),
    sa.Column('specification', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='规格型号'),
    sa.Column('brand', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='品牌'),
    sa.Column('origin', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='产地'),
    sa.Column('quantity', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='数量'),
    sa.Column('unit', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='单位'),
    sa.Column('unit_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='单价'),
    sa.Column('amount', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='金额'),
    sa.Column('remarks', sa.TEXT(), autoincrement=False, nullable=True, comment='备注'),
    sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('is_synced_to_milvus', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='是否已同步到Milvus'),
    sa.PrimaryKeyConstraint('id', name='materials_pkey')
    )
    op.create_index('ix_materials_product_name', 'materials', ['product_name'], unique=False)
    op.create_index('ix_materials_is_synced_to_milvus', 'materials', ['is_synced_to_milvus'], unique=False)
    op.create_index('ix_materials_id', 'materials', ['id'], unique=False)
    # ### end Alembic commands ###
