# VS Code 启动配置说明

本文档说明如何使用VS Code启动和调试项目中的各个服务。

## 架构说明

本项目采用以下架构：

1. **Celery集成在主应用程序中** - Celery Worker和Beat使用主应用程序中的配置和任务定义
2. **Flower独立运行** - Flower作为监控工具独立运行，不集成到主应用程序中

这种架构的优点是：
- Celery与主应用程序共享配置和资源，便于管理
- Flower独立运行，不会影响主应用程序的性能和稳定性
- 可以根据需要单独启动或停止各个组件

## 启动方式

### 方式一：使用任务（推荐）

1. 打开命令面板（Ctrl+Shift+P）
2. 输入"Tasks: Run Task"并选择
3. 从列表中选择要运行的任务

可用的任务包括：

- **启动API服务** - 启动FastAPI服务
- **启动Web前端** - 启动Web前端开发服务器
- **启动Celery Worker** - 启动Celery Worker进程
- **启动Celery Beat** - 启动Celery Beat进程
- **启动Flower** - 启动Flower监控界面

组合任务：

- **启动API + Worker** - 并行启动API服务和Celery Worker
- **启动API + Worker + Beat** - 并行启动API服务、Celery Worker和Beat
- **启动API + Worker + Flower** - 并行启动API服务、Celery Worker和Flower
- **启动API + Worker + Beat + Flower** - 并行启动API服务、Celery Worker、Beat和Flower
- **启动Worker + Beat + Flower** - 并行启动Celery Worker、Beat和Flower
- **启动所有服务** - 并行启动所有服务

### 方式二：使用调试启动配置

1. 打开VS Code的"运行和调试"面板（Ctrl+Shift+D）
2. 从下拉菜单中选择要启动的配置
3. 点击绿色的运行按钮或按F5启动调试

可用的配置包括：

- **Python: FastAPI** - 启动FastAPI服务
- **Python: Celery Worker** - 启动Celery Worker进程
- **Python: Celery Beat** - 启动Celery Beat进程
- **Python: Flower** - 启动Flower监控界面

组合配置：

- **FastAPI + Celery Worker** - 同时启动FastAPI和Celery Worker
- **FastAPI + Celery Worker + Beat** - 同时启动FastAPI、Celery Worker和Beat
- **FastAPI + Celery Worker + Flower** - 同时启动FastAPI、Celery Worker和Flower
- **FastAPI + Celery Worker + Beat + Flower** - 同时启动FastAPI、Celery Worker、Beat和Flower
- **Celery Worker + Beat + Flower** - 同时启动Celery Worker、Beat和Flower

### 方式三：使用快捷键

可以使用以下快捷键快速启动服务：

- **Ctrl+Shift+A** - 启动API服务
- **Ctrl+Shift+W** - 启动Celery Worker
- **Ctrl+Shift+B** - 启动Celery Beat
- **Ctrl+Shift+F** - 启动Flower
- **Ctrl+Shift+V** - 启动Web前端
- **Ctrl+Shift+1** - 启动API + Worker
- **Ctrl+Shift+2** - 启动API + Worker + Beat
- **Ctrl+Shift+3** - 启动API + Worker + Flower
- **Ctrl+Shift+4** - 启动API + Worker + Beat + Flower
- **Ctrl+Shift+5** - 启动所有服务

## 访问服务

- **API服务**：http://localhost:8000
- **Web前端**：http://localhost:3000
- **Flower监控界面**：http://localhost:5555（用户名：admin，密码：admin）

## 注意事项

1. 启动多个服务时，每个服务会在单独的终端中运行
2. 使用调试启动配置可以设置断点和调试代码
3. 使用任务可以并行启动多个服务
4. 如果需要停止服务，可以关闭相应的终端或使用Ctrl+C
