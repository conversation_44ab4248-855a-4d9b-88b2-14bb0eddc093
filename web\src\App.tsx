import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import RouteElement from './router';
import { Layout } from 'antd';

const { Content } = Layout;

export default function App() {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1677ff',
        },
      }}
    >
      <BrowserRouter>
        <Layout>
          <Content>
            <RouteElement />
          </Content>
        </Layout>
      </BrowserRouter>
    </ConfigProvider>
  );
}