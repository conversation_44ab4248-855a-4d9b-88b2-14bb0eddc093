import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, List, Avatar, Card, Spin, message } from 'antd';
import { SendOutlined, UserOutlined, RobotOutlined, CloseOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import styles from './style.module.less';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  displayedContent?: string;
  loading?: boolean;
}

const TYPEWRITER_SPEED = 30;

const PriceChatPage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const typewriterIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const userScrolledRef = useRef<boolean>(false);

  const scrollToBottom = () => {
    if (!userScrolledRef.current) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      if (scrollHeight - scrollTop - clientHeight > 30) {
        userScrolledRef.current = true;
      } else {
        userScrolledRef.current = false;
      }
    }
  };

  useEffect(() => {
    scrollToBottom();
    const messagesContainer = messagesContainerRef.current;
    if (messagesContainer) {
      messagesContainer.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (messagesContainer) {
        messagesContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [messages]);

  useEffect(() => {
    if (typewriterIntervalRef.current) {
      clearInterval(typewriterIntervalRef.current);
      typewriterIntervalRef.current = null;
    }

    const lastMessage = messages[messages.length - 1];

    if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.loading) {
      const targetContent = lastMessage.content;
      const currentDisplayed = lastMessage.displayedContent || '';

      if (currentDisplayed.length < targetContent.length) {
        typewriterIntervalRef.current = setInterval(() => {
          setMessages(prevMessages => {
            const updatedMessages = [...prevMessages];
            const lastMsgIndex = updatedMessages.length - 1;
            const currentMsg = updatedMessages[lastMsgIndex];
            
            if (currentMsg && currentMsg.role === 'assistant' && !currentMsg.loading) {
              const currentLength = currentMsg.displayedContent?.length || 0;
              const targetLength = currentMsg.content.length;

              if (currentLength < targetLength) {
                updatedMessages[lastMsgIndex] = {
                  ...currentMsg,
                  displayedContent: currentMsg.content.substring(0, currentLength + 1),
                };
                
                if (!userScrolledRef.current) {
                  requestAnimationFrame(() => {
                    scrollToBottom();
                  });
                }
                
                return updatedMessages;
              } else {
                if (typewriterIntervalRef.current) {
                  clearInterval(typewriterIntervalRef.current);
                  typewriterIntervalRef.current = null;
                }
              }
            } else {
              if (typewriterIntervalRef.current) {
                clearInterval(typewriterIntervalRef.current);
                typewriterIntervalRef.current = null;
              }
            }
            return prevMessages;
          });
        }, TYPEWRITER_SPEED);
      }
    }

    return () => {
      if (typewriterIntervalRef.current) {
        clearInterval(typewriterIntervalRef.current);
        typewriterIntervalRef.current = null;
      }
    };
  }, [messages]);

  const stopResponse = () => {
    if (abortController) {
      abortController.abort();
    }
    if (typewriterIntervalRef.current) {
      clearInterval(typewriterIntervalRef.current);
      typewriterIntervalRef.current = null;
    }
    setMessages(prev => {
      const updated = [...prev];
      const lastMsgIndex = updated.length - 1;
      if (updated[lastMsgIndex]?.role === 'assistant' && updated[lastMsgIndex]?.loading) {
        updated[lastMsgIndex] = {
          ...updated[lastMsgIndex],
          loading: false,
          content: updated[lastMsgIndex].content || '响应已终止',
          displayedContent: updated[lastMsgIndex].displayedContent || '响应已终止'
        };
      }
      return updated;
    });
    setLoading(false);
    setIsSending(false);
  };

  const sendMessage = async () => {
    if (!input.trim()) return;

    if (typewriterIntervalRef.current) {
      clearInterval(typewriterIntervalRef.current);
      typewriterIntervalRef.current = null;
    }

    const userMessage: Message = { role: 'user', content: input };
    const assistantMessage: Message = { role: 'assistant', content: '', displayedContent: '', loading: true };
    
    setMessages(prevMessages => [...prevMessages, userMessage, assistantMessage]);
    setInput('');
    setIsSending(true);
    setLoading(true);

    try {
      const controller = new AbortController();
      setAbortController(controller);

      const response = await fetch('/api/dify-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: input,
          response_mode: "streaming",
          user: "user_id"
        }),
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;

        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.substring(6);
              
              if (data === '[DONE]') {
                setMessages(prev => {
                  const updated = [...prev];
                  const lastMsgIndex = updated.length - 1;
                  if (updated[lastMsgIndex]?.role === 'assistant') {
                    updated[lastMsgIndex] = { ...updated[lastMsgIndex], loading: false };
                  }
                  return updated;
                });
                break;
              }

              try {
                const parsedData = JSON.parse(data);
                const { event } = parsedData;

                switch (event) {
                  case 'message':
                    const { answer } = parsedData;
                    setMessages(prev => {
                      const updated = [...prev];
                      const lastMsgIndex = updated.length - 1;
                      if (updated[lastMsgIndex]?.role === 'assistant') {
                        updated[lastMsgIndex] = {
                          ...updated[lastMsgIndex],
                          content: (updated[lastMsgIndex].content || '') + answer,
                          displayedContent: (updated[lastMsgIndex].displayedContent || '') + answer,
                        };
                      }
                      return updated;
                    });
                    break;
                  case 'message_file':
                    const { url } = parsedData;
                    setMessages(prev => {
                      const updated = [...prev];
                      const lastMsgIndex = updated.length - 1;
                      if (updated[lastMsgIndex]?.role === 'assistant') {
                        updated[lastMsgIndex] = {
                          ...updated[lastMsgIndex],
                          content: (updated[lastMsgIndex].content || '') + `\n![file](${url})\n`,
                          displayedContent: (updated[lastMsgIndex].displayedContent || '') + `\n![file](${url})\n`,
                        };
                      }
                      return updated;
                    });
                    break;
                  case 'message_end':
                    setMessages(prev => {
                      const updated = [...prev];
                      const lastMsgIndex = updated.length - 1;
                      if (updated[lastMsgIndex]?.role === 'assistant') {
                        updated[lastMsgIndex] = { ...updated[lastMsgIndex], loading: false };
                      }
                      return updated;
                    });
                    break;
                  case 'tts_message':
                    // TODO: Implement TTS audio playback
                    break;
                  case 'tts_message_end':
                    // TODO: Handle TTS end event
                    break;
                  case 'message_replace':
                    const { answer: replaceAnswer } = parsedData;
                    setMessages(prev => {
                      const updated = [...prev];
                      const lastMsgIndex = updated.length - 1;
                      if (updated[lastMsgIndex]?.role === 'assistant') {
                        updated[lastMsgIndex] = {
                          ...updated[lastMsgIndex],
                          content: replaceAnswer,
                          displayedContent: replaceAnswer,
                        };
                      }
                      return updated;
                    });
                    break;
                  case 'workflow_started':
                    // TODO: Handle workflow started event
                    break;
                  case 'node_started':
                    // TODO: Handle node started event
                    break;
                  case 'node_finished':
                    // TODO: Handle node finished event
                    break;
                  case 'workflow_finished':
                    // TODO: Handle workflow finished event
                    break;
                  case 'error':
                    const { message: errorMessage } = parsedData;
                    throw new Error(`Stream error: ${errorMessage}`);
                  case 'ping':
                    // Ignore ping events
                    break;
                  default:
                    console.warn(`Unknown event type: ${event}`);
                }
              } catch (e) {
                console.error('Error parsing data:', e);
              }
            }
          }
        }
      }
      setMessages(prev => {
        const updated = [...prev];
        const lastMsgIndex = updated.length - 1;
        if (updated[lastMsgIndex]?.role === 'assistant' && updated[lastMsgIndex]?.loading) {
          updated[lastMsgIndex] = { ...updated[lastMsgIndex], loading: false };
        }
        return updated;
      });

    } catch (error) {
      console.error('发送消息时出错:', error);
      message.error('发送消息失败，请稍后重试');
      setIsSending(false);
      setMessages(prev => {
        const updated = [...prev];
        const lastMsgIndex = updated.length - 1;
         if (updated[lastMsgIndex]?.role === 'assistant') {
            updated[lastMsgIndex] = {
              role: 'assistant',
              content: '抱歉，我遇到了一些问题，无法回应您的消息。请稍后再试。',
              displayedContent: '抱歉，我遇到了一些问题，无法回应您的消息。请稍后再试。',
              loading: false,
            };
         }
        return updated;
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles['price-chat-page-container']}>
      <Card title="价格咨询助手" className={styles['price-chat-card']} bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}>
        <div className={styles['messages-container']} ref={messagesContainerRef}>
          {messages.length === 0 ? (
            <div className={styles['empty-chat']}>
              <p>欢迎使用价格咨询助手！</p>
            </div>
          ) : (
            <List
              itemLayout="horizontal"
              dataSource={messages}
              renderItem={(message, index) => (
                <List.Item key={index} className={`${styles['message-item']} ${styles[message.role]}`}>
                  {message.role === 'assistant' && (
                    <Avatar 
                      shape='circle'
                      src="https://www.shecc.com:8443/static/logo/5.png"
                      size="large"
                      icon={<RobotOutlined />}
                      className={styles['avatar-left']}
                    />
                  )}
                  <div className={styles['message-content-wrapper']}>
                    {message.loading && message.role === 'assistant' && !message.displayedContent ? (
                      <Spin size="small" />
                    ) : (
                      <div className={styles['message-content']}>
                        {message.role === 'assistant' ? (
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {message.displayedContent || ''}
                          </ReactMarkdown>
                        ) : (
                          message.content
                        )}
                      </div>
                    )}
                  </div>
                  {message.role === 'user' && (
                    <Avatar 
                      shape='circle'
                      src="https://www.shecc.com:8443/static/logo/5.png"
                      size="large"
                      icon={<UserOutlined />}
                      className={styles['avatar-right']}
                    />
                  )}
                </List.Item>
              )}
            />
          )}
          <div ref={messagesEndRef} />
        </div>
        
        <div className={styles['input-container']}>
          <Input.TextArea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className={styles['chat-input']}
            placeholder="请输入您要咨询的价格问题..."
            autoSize={{ minRows: 4, maxRows: 4 }}
            onPressEnter={(e) => {
              if (!e.shiftKey && !loading) {
                e.preventDefault();
                sendMessage();
              }
            }}
            disabled={loading}
          />
          <div style={{ display: 'flex', gap: 8 }}>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={sendMessage}
              loading={loading}
              disabled={!input.trim() || loading || isSending}
              className={styles['send-button']}
              style={{ display: isSending ? 'none' : 'inline-flex' }}
            >
              发送
            </Button>
            <Button
              danger
              icon={<CloseOutlined />}
              onClick={stopResponse}
              disabled={!loading || !isSending}
              className={styles['send-button']}
              style={{ display: isSending ? 'inline-flex' : 'none' }}
            >
              终止
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PriceChatPage;
