# 阶段1：前端构建
FROM harbor.shecc.com/baseimages/node18:latest as frontend-builder
WORKDIR /app
COPY web/package.json web/package-lock.json ./
RUN pnpm install
COPY web .
RUN pnpm run build

# 阶段2：Python环境
FROM harbor.shecc.com/baseimages/python311:latest as python-builder
WORKDIR /app
COPY api/requirements.txt .
RUN apt-get update && apt-get install -y --no-install-recommends gcc python3-dev \
    && pip install --user -r requirements.txt \
    && apt-get purge -y --auto-remove gcc python3-dev


COPY api .

# 复制前端构建结果
COPY --from=frontend-builder /app/dist /app/www

# 设置环境变量
ENV PATH=/root/.local/bin:$PATH
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
