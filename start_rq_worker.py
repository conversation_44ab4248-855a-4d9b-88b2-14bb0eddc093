#!/usr/bin/env python
"""
RQ Worker启动脚本
用于启动RQ Worker处理任务
"""

import os
import sys
import argparse

# 默认Redis连接
REDIS_URL = 'redis://:yhb25IEz@10.100.4.17:6379/3'

# 默认队列
DEFAULT_QUEUES = ['default', 'high', 'low']

def start_worker(redis_url, queues, name=None, workers=1):
    """
    启动RQ Worker

    Args:
        redis_url: Redis连接URL
        queues: 要监听的队列列表
        name: Worker名称前缀
        workers: Worker数量
    """
    import sys
    import subprocess

    # 打印启动信息
    print(f"启动RQ Worker...")
    print(f"Redis URL: {redis_url}")
    print(f"监听队列: {', '.join(queues)}")
    print(f"Worker数量: {workers}")
    if name:
        print(f"Worker名称前缀: {name}")
    print(f"按Ctrl+C停止Worker")

    # 构建命令
    for i in range(workers):
        worker_name = f"{name}_{i+1}" if name else None

        cmd = [sys.executable, "-m", "rq.cli", "worker", "--url", redis_url]

        if worker_name:
            cmd.extend(["--name", worker_name])

        # 添加队列
        cmd.extend(queues)

        # 启动Worker进程
        print(f"启动Worker {i+1}: {' '.join(cmd)}")
        subprocess.Popen(cmd)

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='启动RQ Worker')
    parser.add_argument('--url', default=REDIS_URL, help='Redis连接URL')
    parser.add_argument('--queues', default=','.join(DEFAULT_QUEUES), help='要监听的队列，用逗号分隔')
    parser.add_argument('--name', help='Worker名称前缀')
    parser.add_argument('--workers', type=int, default=1, help='Worker数量')

    args = parser.parse_args()

    # 解析队列列表
    queues = args.queues.split(',')

    # 启动Worker
    start_worker(args.url, queues, args.name, args.workers)
