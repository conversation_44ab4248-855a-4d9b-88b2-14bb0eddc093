from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from typing import Annotated, Literal, List, Dict, Any, Optional
from pydantic import BaseModel

from ..core.database import get_db
from ..crud.item import get_items
from ..schemas.item import Materials
from ..core.vector_db import get_vector_db_client, VectorDBClient # 导入向量数据库客户端
from ..core.logging_config import get_logger # 导入日志记录器

router = APIRouter(tags=["物料"], prefix="/api/items")
logger = get_logger("items_router") # 创建日志记录器实例

class ItemsResponse(BaseModel):
    items: list[Materials]
    total: int

# 定义向量搜索结果的 Pydantic 模型
class VectorSearchResultItem(BaseModel):
    id: Optional[int] = None
    product_name: Optional[str] = None
    contract_id: Optional[str] = None
    contract_number: Optional[str] = None
    unit_price: Optional[float] = None
    specification: Optional[str] = None
    brand: Optional[str] = None
    origin: Optional[str] = None
    warranty_months: Optional[int] = None
    includes_delivery_installation: Optional[str] = None
    score: float

class VectorSearchResponse(BaseModel):
    results: List[VectorSearchResultItem]

@router.get("/search", response_model=VectorSearchResponse)
async def search_items_vector(
    q: Annotated[str, Query(description="搜索查询词", min_length=1)],
    limit: Annotated[int, Query(description="返回结果数量", ge=1, le=50)] = 10,
    sparse_weight: Annotated[float, Query(description="稀疏向量权重 (用于混合搜索)", ge=0.0, le=1.0)] = 1,
    dense_weight: Annotated[float, Query(description="稠密向量权重 (用于混合搜索)", ge=0.0, le=1.0)] = 0,
    vector_db_client: VectorDBClient = Depends(get_vector_db_client) # 依赖注入
):
    """
    使用向量数据库进行物料搜索 (混合搜索)

    ## 参数
    - q: 搜索查询词
    - limit: 返回结果数量限制
    - sparse_weight: 稀疏向量在混合搜索中的权重 (0.0 - 1.0)
    - dense_weight: 稠密向量在混合搜索中的权重 (0.0 - 1.0)

    ## 返回值
    返回按相关性分数排序的物料列表
    """
    logger.info(f"收到向量搜索请求: q='{q}', limit={limit}, sparse_weight={sparse_weight}, dense_weight={dense_weight}")
    try:
        sparse_weight = 0
        dense_weight = 1    
        search_results = await vector_db_client.search_items(
            query_text=q,
            limit=limit,
            sparse_weight=sparse_weight,
            dense_weight=dense_weight
        )
        logger.info(f"向量搜索找到 {len(search_results)} 条结果")
        return {"results": search_results}
    except Exception as e:
        logger.error(f"向量搜索时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"向量搜索失败: {str(e)}")


@router.get("/", response_model=ItemsResponse)
def query_items(
    skip: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 10,
    product_name: Annotated[str | None, Query(min_length=1)] = None,
    specification: Annotated[str | None, Query(min_length=1)] = None,
    brand: Annotated[str | None, Query(min_length=1)] = None,
    origin: Annotated[str | None, Query(min_length=1)] = None,
    sort_by: Annotated[str | None, Query(
        description="排序字段",
        examples=["product_name", "specification", "brand", "created_at"]
    )] = None,
    sort_order: Annotated[Literal["asc", "desc"], Query(
        description="- 排序顺序",
        examples=["asc", "desc"]
    )] = "asc",
    db: Session = Depends(get_db)
):
    """查询物料
    
    ## 参数
    - skip: 跳过的记录数，用于分页
    - limit: 每页显示的记录数
    - product_name: 按产品名称模糊查询
    - specification: 按规格型号模糊查询
    - brand: 按品牌模糊查询  
    - origin: 按产地模糊查询
    - sort_by: 排序字段
    - sort_order: 排序顺序，"asc" 升序，"desc" 降序
    
    ## 返回值
    返回符合条件的物料列表
    """
    db_result = get_items(
        db,
        skip=skip,
        limit=limit,
        product_name=product_name,
        specification=specification,
        brand=brand,
        origin=origin,
        sort_by=sort_by,
        sort_order=sort_order
    )
    return {
        "items": db_result["items"],
        "total": db_result["total"]
    }
