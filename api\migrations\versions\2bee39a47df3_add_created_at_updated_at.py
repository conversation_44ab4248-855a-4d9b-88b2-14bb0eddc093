"""add_created_at_updated_at

Revision ID: 2bee39a47df3
Revises: 
Create Date: 2025-04-03 12:59:09.366656

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2bee39a47df3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # First drop tables with foreign key dependencies
    op.drop_index('ix_price_history_id', table_name='price_history')
    op.execute('DROP TABLE IF EXISTS price_history CASCADE')
    op.drop_index('ix_supplier_quotes_contract_number', table_name='supplier_quotes')
    op.drop_index('ix_supplier_quotes_id', table_name='supplier_quotes')
    op.drop_index('ix_supplier_quotes_project_number', table_name='supplier_quotes')
    op.execute('DROP TABLE IF EXISTS supplier_quotes CASCADE')
    
    # Then drop other tables
    op.drop_index('ix_materials_id', table_name='materials')
    op.drop_index('ix_materials_product_name', table_name='materials')
    op.execute('DROP TABLE IF EXISTS materials CASCADE')
    op.drop_index('ix_equipment_items_id', table_name='equipment_items')
    op.drop_index('ix_equipment_items_product_name', table_name='equipment_items')
    op.drop_index('ix_equipment_items_serial_number', table_name='equipment_items')
    op.execute('DROP TABLE IF EXISTS equipment_items CASCADE')
    op.drop_index('ix_products_category', table_name='products')
    op.drop_index('ix_products_code', table_name='products')
    op.drop_index('ix_products_id', table_name='products')
    op.drop_index('ix_products_name', table_name='products')
    op.execute('DROP TABLE IF EXISTS products CASCADE')
    op.drop_index('ix_suppliers_code', table_name='suppliers')
    op.drop_index('ix_suppliers_id', table_name='suppliers')
    op.drop_index('ix_suppliers_name', table_name='suppliers')
    op.execute('DROP TABLE IF EXISTS suppliers CASCADE')
    op.drop_index('ix_batches_batch_number', table_name='batches')
    op.drop_index('ix_batches_id', table_name='batches')
    op.execute('DROP TABLE IF EXISTS batches CASCADE')
    
    # 重新创建表，添加created_at和updated_at字段
    op.create_table('suppliers',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False, comment='供应商名称'),
    sa.Column('code', sa.VARCHAR(length=50), nullable=True, comment='供应商编码'),
    sa.Column('contact_person', sa.VARCHAR(length=100), nullable=True, comment='联系人'),
    sa.Column('contact_phone', sa.VARCHAR(length=50), nullable=True, comment='联系电话'),
    sa.Column('email', sa.VARCHAR(length=100), nullable=True, comment='电子邮箱'),
    sa.Column('address', sa.TEXT(), nullable=True, comment='地址'),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True, comment='是否活跃'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='suppliers_pkey')
    )
    op.create_index('ix_suppliers_code', 'suppliers', ['code'], unique=True)
    op.create_index('ix_suppliers_id', 'suppliers', ['id'], unique=False)
    op.create_index('ix_suppliers_name', 'suppliers', ['name'], unique=False)
    
    op.create_table('products',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False, comment='产品名称'),
    sa.Column('code', sa.VARCHAR(length=50), nullable=True, comment='产品编码'),
    sa.Column('category', sa.VARCHAR(length=100), nullable=True, comment='产品类别'),
    sa.Column('specification', sa.VARCHAR(length=255), nullable=True, comment='规格'),
    sa.Column('unit', sa.VARCHAR(length=50), nullable=True, comment='单位'),
    sa.Column('description', sa.TEXT(), nullable=True, comment='描述'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='products_pkey')
    )
    op.create_index('ix_products_category', 'products', ['category'], unique=False)
    op.create_index('ix_products_code', 'products', ['code'], unique=True)
    op.create_index('ix_products_id', 'products', ['id'], unique=False)
    op.create_index('ix_products_name', 'products', ['name'], unique=False)
    
    op.create_table('equipment_items',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('serial_number', sa.INTEGER(), nullable=True, comment='序号'),
    sa.Column('product_name', sa.VARCHAR(length=255), nullable=True, comment='产品名称'),
    sa.Column('specification', sa.VARCHAR(length=255), nullable=True, comment='规格型号'),
    sa.Column('brand', sa.VARCHAR(length=100), nullable=True, comment='品牌'),
    sa.Column('origin', sa.VARCHAR(length=100), nullable=True, comment='产地'),
    sa.Column('quantity', sa.DOUBLE_PRECISION(precision=53), nullable=True, comment='数量'),
    sa.Column('unit', sa.VARCHAR(length=50), nullable=True, comment='单位'),
    sa.Column('unit_price', sa.DOUBLE_PRECISION(precision=53), nullable=True, comment='单价'),
    sa.Column('amount', sa.DOUBLE_PRECISION(precision=53), nullable=True, comment='金额'),
    sa.Column('remarks', sa.TEXT(), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='equipment_items_pkey')
    )
    op.create_index('ix_equipment_items_id', 'equipment_items', ['id'], unique=False)
    op.create_index('ix_equipment_items_product_name', 'equipment_items', ['product_name'], unique=False)
    op.create_index('ix_equipment_items_serial_number', 'equipment_items', ['serial_number'], unique=False)
    
    op.create_table('materials',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_name', sa.VARCHAR(length=255), nullable=True, comment='产品名称'),
    sa.Column('specification', sa.VARCHAR(length=255), nullable=True, comment='规格型号'),
    sa.Column('brand', sa.VARCHAR(length=100), nullable=True, comment='品牌'),
    sa.Column('origin', sa.VARCHAR(length=100), nullable=True, comment='产地'),
    sa.Column('quantity', sa.DOUBLE_PRECISION(precision=53), nullable=True, comment='数量'),
    sa.Column('unit', sa.VARCHAR(length=50), nullable=True, comment='单位'),
    sa.Column('unit_price', sa.DOUBLE_PRECISION(precision=53), nullable=True, comment='单价'),
    sa.Column('amount', sa.DOUBLE_PRECISION(precision=53), nullable=True, comment='金额'),
    sa.Column('remarks', sa.TEXT(), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='materials_pkey')
    )
    op.create_index('ix_materials_id', 'materials', ['id'], unique=False)
    op.create_index('ix_materials_product_name', 'materials', ['product_name'], unique=False)
    
    op.create_table('batches',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('batch_number', sa.VARCHAR(length=100), nullable=False, comment='批次号'),
    sa.Column('supplier_id', sa.INTEGER(), nullable=False, comment='供应商ID'),
    sa.Column('production_date', sa.DateTime(), nullable=True, comment='生产日期'),
    sa.Column('arrival_date', sa.DateTime(), nullable=True, comment='到货日期'),
    sa.Column('expiry_date', sa.DateTime(), nullable=True, comment='有效期'),
    sa.Column('notes', sa.VARCHAR(length=255), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], name='batches_supplier_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='batches_pkey')
    )
    op.create_index('ix_batches_batch_number', 'batches', ['batch_number'], unique=False)
    op.create_index('ix_batches_id', 'batches', ['id'], unique=False)
    
    op.create_table('supplier_quotes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_id', sa.INTEGER(), nullable=False, comment='产品ID'),
    sa.Column('batch_id', sa.INTEGER(), nullable=False, comment='批次ID'),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), nullable=False, comment='价格'),
    sa.Column('currency', sa.VARCHAR(length=10), nullable=True, comment='货币单位'),
    sa.Column('effective_date', sa.DateTime(), nullable=False, comment='生效日期'),
    sa.Column('end_date', sa.DateTime(), nullable=True, comment='结束日期'),
    sa.Column('price_type', sa.VARCHAR(length=50), nullable=True, comment='价格类型（标准价、促销价等）'),
    sa.Column('contract_number', sa.VARCHAR(length=50), nullable=True, comment='合同编号'),
    sa.Column('project_number', sa.VARCHAR(length=50), nullable=True, comment='项目编号'),
    sa.Column('is_accepted', sa.BOOLEAN(), nullable=True, comment='是否采纳'),
    sa.Column('source', sa.VARCHAR(length=100), nullable=True, comment='数据来源'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['batch_id'], ['batches.id'], name='supplier_quotes_batch_id_fkey'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name='supplier_quotes_product_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='supplier_quotes_pkey')
    )
    op.create_index('ix_supplier_quotes_contract_number', 'supplier_quotes', ['contract_number'], unique=False)
    op.create_index('ix_supplier_quotes_id', 'supplier_quotes', ['id'], unique=False)
    op.create_index('ix_supplier_quotes_project_number', 'supplier_quotes', ['project_number'], unique=False)
    
    op.create_table('price_history',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_id', sa.INTEGER(), nullable=False, comment='产品ID'),
    sa.Column('batch_id', sa.INTEGER(), nullable=False, comment='批次ID'),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), nullable=False, comment='价格'),
    sa.Column('currency', sa.VARCHAR(length=10), nullable=True, comment='货币单位'),
    sa.Column('effective_date', sa.DateTime(), nullable=False, comment='生效日期'),
    sa.Column('end_date', sa.DateTime(), nullable=True, comment='结束日期'),
    sa.Column('price_type', sa.VARCHAR(length=50), nullable=True, comment='价格类型（标准价、促销价等）'),
    sa.Column('source', sa.VARCHAR(length=100), nullable=True, comment='数据来源'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['batch_id'], ['batches.id'], name='price_history_batch_id_fkey'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name='price_history_product_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='price_history_pkey')
    )
    op.create_index('ix_price_history_id', 'price_history', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('batches',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('batches_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('batch_number', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='批次号'),
    sa.Column('supplier_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='供应商ID'),
    sa.Column('production_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='生产日期'),
    sa.Column('arrival_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='到货日期'),
    sa.Column('expiry_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='有效期'),
    sa.Column('notes', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='备注'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], name='batches_supplier_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='batches_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_batches_id', 'batches', ['id'], unique=False)
    op.create_index('ix_batches_batch_number', 'batches', ['batch_number'], unique=False)
    op.create_table('supplier_quotes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='产品ID'),
    sa.Column('batch_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='批次ID'),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False, comment='价格'),
    sa.Column('currency', sa.VARCHAR(length=10), autoincrement=False, nullable=True, comment='货币单位'),
    sa.Column('effective_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='生效日期'),
    sa.Column('end_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='结束日期'),
    sa.Column('price_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='价格类型（标准价、促销价等）'),
    sa.Column('contract_number', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='合同编号'),
    sa.Column('project_number', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='项目编号'),
    sa.Column('is_accepted', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否采纳'),
    sa.Column('source', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='数据来源'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['batch_id'], ['batches.id'], name='supplier_quotes_batch_id_fkey'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name='supplier_quotes_product_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='supplier_quotes_pkey')
    )
    op.create_index('ix_supplier_quotes_project_number', 'supplier_quotes', ['project_number'], unique=False)
    op.create_index('ix_supplier_quotes_id', 'supplier_quotes', ['id'], unique=False)
    op.create_index('ix_supplier_quotes_contract_number', 'supplier_quotes', ['contract_number'], unique=False)
    op.create_table('price_history',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='产品ID'),
    sa.Column('batch_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='批次ID'),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False, comment='价格'),
    sa.Column('currency', sa.VARCHAR(length=10), autoincrement=False, nullable=True, comment='货币单位'),
    sa.Column('effective_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='生效日期'),
    sa.Column('end_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='结束日期'),
    sa.Column('price_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='价格类型（标准价、促销价等）'),
    sa.Column('source', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='数据来源'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['batch_id'], ['batches.id'], name='price_history_batch_id_fkey'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name='price_history_product_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='price_history_pkey')
    )
    op.create_index('ix_price_history_id', 'price_history', ['id'], unique=False)
    op.create_table('suppliers',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='供应商名称'),
    sa.Column('code', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='供应商编码'),
    sa.Column('contact_person', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='联系人'),
    sa.Column('contact_phone', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='联系电话'),
    sa.Column('email', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='电子邮箱'),
    sa.Column('address', sa.TEXT(), autoincrement=False, nullable=True, comment='地址'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否活跃'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='suppliers_pkey')
    )
    op.create_index('ix_suppliers_name', 'suppliers', ['name'], unique=False)
    op.create_index('ix_suppliers_id', 'suppliers', ['id'], unique=False)
    op.create_index('ix_suppliers_code', 'suppliers', ['code'], unique=True)
    op.create_table('products',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='产品名称'),
    sa.Column('code', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='产品编码'),
    sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='产品类别'),
    sa.Column('specification', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='规格'),
    sa.Column('unit', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='单位'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='描述'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='products_pkey')
    )
    op.create_index('ix_products_name', 'products', ['name'], unique=False)
    op.create_index('ix_products_id', 'products', ['id'], unique=False)
    op.create_index('ix_products_code', 'products', ['code'], unique=True)
    op.create_index('ix_products_category', 'products', ['category'], unique=False)
    op.create_table('equipment_items',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('serial_number', sa.INTEGER(), autoincrement=False, nullable=True, comment='序号'),
    sa.Column('product_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='产品名称'),
    sa.Column('specification', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='规格型号'),
    sa.Column('brand', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='品牌'),
    sa.Column('origin', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='产地'),
    sa.Column('quantity', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='数量'),
    sa.Column('unit', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='单位'),
    sa.Column('unit_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='单价'),
    sa.Column('amount', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='金额'),
    sa.Column('remarks', sa.TEXT(), autoincrement=False, nullable=True, comment='备注'),
    sa.PrimaryKeyConstraint('id', name='equipment_items_pkey')
    )
    op.create_index('ix_equipment_items_serial_number', 'equipment_items', ['serial_number'], unique=False)
    op.create_index('ix_equipment_items_product_name', 'equipment_items', ['product_name'], unique=False)
    op.create_index('ix_equipment_items_id', 'equipment_items', ['id'], unique=False)
    op.create_table('materials',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('product_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='产品名称'),
    sa.Column('specification', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='规格型号'),
    sa.Column('brand', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='品牌'),
    sa.Column('origin', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='产地'),
    sa.Column('quantity', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='数量'),
    sa.Column('unit', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='单位'),
    sa.Column('unit_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='单价'),
    sa.Column('amount', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='金额'),
    sa.Column('remarks', sa.TEXT(), autoincrement=False, nullable=True, comment='备注'),
    sa.PrimaryKeyConstraint('id', name='materials_pkey')
    )
    op.create_index('ix_materials_product_name', 'materials', ['product_name'], unique=False)
    op.create_index('ix_materials_id', 'materials', ['id'], unique=False)
    # ### end Alembic commands ###
