import React, { useState, useEffect } from 'react';
import {
  Card,
  Upload,
  Button,
  Form,
  InputNumber,
  Input,
  message,
  Progress,
  Table,
  Tag,
  Space,
  Modal,
  Descriptions,
  Tabs,
  Alert,
  Row,
  Col,
  Statistic,
  Typography,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  DownloadOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';
import TaskMonitor from './TaskMonitor';
import DemoPage from './DemoPage';
import './style.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface TaskInfo {
  task_id: string;
  status: string;
  filename?: string;
  total_rows?: number;
  created_at?: string;
  expires_at?: string;
  progress?: number;
  total_batches?: number;
  completed_batches?: number;
  failed_batches?: number;
  summary?: any;
}

interface UploadFormData {
  header_row: number;
  batch_size: number;
  retention_days: number;
  description: string;
}

export default function ExcelMaterialPage() {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [tasks, setTasks] = useState<TaskInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskInfo | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('upload');
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [showTaskMonitor, setShowTaskMonitor] = useState(false);

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/excel-material/tasks?limit=50');
      setTasks(response.data.tasks || []);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取任务列表
  useEffect(() => {
    fetchTasks();
    // 设置定时刷新
    const interval = setInterval(fetchTasks, 10000); // 每10秒刷新一次
    return () => clearInterval(interval);
  }, []);

  // 文件上传配置
  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.endsWith('.xlsx') ||
                     file.name.endsWith('.xls');

      if (!isExcel) {
        message.error('只能上传Excel文件！');
        return false;
      }

      const isLt50M = file.size! / 1024 / 1024 < 50;
      if (!isLt50M) {
        message.error('文件大小不能超过50MB！');
        return false;
      }

      setFileList([file]);
      return false; // 阻止自动上传
    },
    fileList,
    onRemove: () => {
      setFileList([]);
    },
    maxCount: 1,
  };

  // 提交上传
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error('请选择要上传的Excel文件');
      return;
    }

    try {
      const values = await form.validateFields();
      setUploading(true);

      const formData = new FormData();
      formData.append('file', fileList[0] as any);
      formData.append('request_data', JSON.stringify(values));

      const response = await axios.post('/api/excel-material/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('文件上传成功，处理任务已启动');
      setFileList([]);
      form.resetFields();

      // 设置当前任务ID并显示监控
      setCurrentTaskId(response.data.task_id);
      setShowTaskMonitor(true);
      setActiveTab('monitor');
      fetchTasks();
    } catch (error: any) {
      console.error('上传失败:', error);
      message.error(error.response?.data?.detail || '上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 查看任务详情
  const showTaskDetail = async (taskId: string) => {
    try {
      const response = await axios.get(`/api/excel-material/task/${taskId}/status`);
      setSelectedTask(response.data);
      setDetailModalVisible(true);
    } catch (error) {
      message.error('获取任务详情失败');
    }
  };

  // 合并任务结果
  const mergeTaskResults = async (taskId: string) => {
    try {
      await axios.post(`/api/excel-material/task/${taskId}/merge`);
      message.success('结果合并任务已启动');
      fetchTasks();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '合并失败');
    }
  };

  // 下载结果文件
  const downloadResult = async (taskId: string) => {
    try {
      const response = await axios.get(`/api/excel-material/task/${taskId}/download`, {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `材料处理结果_${taskId}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success('文件下载成功');
    } catch (error: any) {
      message.error(error.response?.data?.detail || '下载失败');
    }
  };

  // 删除任务
  const deleteTask = async (taskId: string) => {
    try {
      await axios.delete(`/api/excel-material/task/${taskId}`);
      message.success('任务已删除');
      fetchTasks();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '删除失败');
    }
  };

  // 任务完成回调
  const handleTaskComplete = (taskId: string) => {
    message.success('任务处理完成！');
    fetchTasks();
  };

  // 任务错误回调
  const handleTaskError = (taskId: string, error: string) => {
    message.error(`任务处理失败: ${error}`);
    fetchTasks();
  };

  // 监控任务
  const monitorTask = (taskId: string) => {
    setCurrentTaskId(taskId);
    setShowTaskMonitor(true);
    setActiveTab('monitor');
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig: Record<string, { color: string; text: string; icon: React.ReactNode }> = {
      submitted: { color: 'blue', text: '已提交', icon: <ClockCircleOutlined /> },
      processing: { color: 'orange', text: '处理中', icon: <SyncOutlined spin /> },
      ready_for_merge: { color: 'cyan', text: '待合并', icon: <ExclamationCircleOutlined /> },
      completed: { color: 'green', text: '已完成', icon: <CheckCircleOutlined /> },
      partial_success: { color: 'yellow', text: '部分成功', icon: <ExclamationCircleOutlined /> },
      failed: { color: 'red', text: '失败', icon: <ExclamationCircleOutlined /> },
      merge_failed: { color: 'red', text: '合并失败', icon: <ExclamationCircleOutlined /> },
    };

    const config = statusConfig[status] || { color: 'default', text: status, icon: null };
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 任务表格列定义
  const taskColumns: ColumnsType<TaskInfo> = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 200,
      render: (text) => (
        <Text code copyable={{ text }}>
          {text.substring(0, 16)}...
        </Text>
      ),
    },
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress, record) => (
        <Progress
          percent={Math.round(progress || 0)}
          size="small"
          status={record.status === 'failed' ? 'exception' : 'active'}
        />
      ),
    },
    {
      title: '数据行数',
      dataIndex: 'total_rows',
      key: 'total_rows',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showTaskDetail(record.task_id)}
            />
          </Tooltip>

          {['submitted', 'processing', 'ready_for_merge'].includes(record.status) && (
            <Tooltip title="监控任务">
              <Button
                type="text"
                icon={<ClockCircleOutlined />}
                onClick={() => monitorTask(record.task_id)}
              />
            </Tooltip>
          )}

          {record.status === 'ready_for_merge' && (
            <Tooltip title="合并结果">
              <Button
                type="text"
                icon={<SyncOutlined />}
                onClick={() => mergeTaskResults(record.task_id)}
              />
            </Tooltip>
          )}

          {record.status === 'completed' && (
            <Tooltip title="下载结果">
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => downloadResult(record.task_id)}
              />
            </Tooltip>
          )}

          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => deleteTask(record.task_id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除任务">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="excel-material-page">
      <Title level={2}>
        <FileExcelOutlined /> Excel材料处理
      </Title>

      <Paragraph>
        上传Excel材料文件，系统将自动进行分批处理、价格查询和数据库匹配，生成包含最终价格的结果文件。
      </Paragraph>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="使用说明" key="demo">
          <DemoPage />
        </TabPane>

        <TabPane tab="文件上传" key="upload">
          <Card title="上传Excel文件" className="upload-card">
            <Alert
              message="支持的Excel格式"
              description="请确保Excel文件包含以下列：名称（必需）、型号、规格、数量（必需）、价格、备注。第一行应为标题行。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Form
              form={form}
              layout="vertical"
              initialValues={{
                header_row: 0,
                batch_size: 50,
                retention_days: 30,
                description: '',
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Excel文件"
                    required
                  >
                    <Upload {...uploadProps}>
                      <Button icon={<UploadOutlined />}>选择Excel文件</Button>
                    </Upload>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="description"
                    label="任务描述"
                  >
                    <Input placeholder="可选的任务描述" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="header_row"
                    label="标题行"
                    tooltip="标题行在Excel中的行号（从0开始）"
                  >
                    <InputNumber min={0} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="batch_size"
                    label="批处理大小"
                    tooltip="每批处理的数据行数，建议50-100行"
                  >
                    <InputNumber min={10} max={200} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="retention_days"
                    label="保留天数"
                    tooltip="处理结果的保留天数"
                  >
                    <InputNumber min={1} max={90} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  onClick={handleUpload}
                  loading={uploading}
                  disabled={fileList.length === 0}
                  size="large"
                >
                  开始处理
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab="任务管理" key="tasks">
          <Card
            title="处理任务列表"
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchTasks}
                loading={loading}
              >
                刷新
              </Button>
            }
          >
            <Table
              columns={taskColumns}
              dataSource={tasks}
              rowKey="task_id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个任务`,
              }}
            />
          </Card>
        </TabPane>

        {showTaskMonitor && currentTaskId && (
          <TabPane tab="任务监控" key="monitor">
            <TaskMonitor
              taskId={currentTaskId}
              onComplete={handleTaskComplete}
              onError={handleTaskError}
            />
          </TabPane>
        )}
      </Tabs>

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTask && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Statistic
                  title="总批次"
                  value={selectedTask.total_batches || 0}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="已完成"
                  value={selectedTask.completed_batches || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="失败批次"
                  value={selectedTask.failed_batches || 0}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>

            <Descriptions bordered column={2}>
              <Descriptions.Item label="任务ID">
                <Text code copyable>
                  {selectedTask.task_id}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedTask.status)}
              </Descriptions.Item>
              <Descriptions.Item label="文件名">
                {selectedTask.filename || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="数据行数">
                {selectedTask.total_rows || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {selectedTask.created_at ? new Date(selectedTask.created_at).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="过期时间">
                {selectedTask.expires_at ? new Date(selectedTask.expires_at).toLocaleString() : '-'}
              </Descriptions.Item>
            </Descriptions>

            {selectedTask.summary && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>处理统计</Title>
                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="总处理数"
                      value={selectedTask.summary.total_processed || 0}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="成功数"
                      value={selectedTask.summary.success_count || 0}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="失败数"
                      value={selectedTask.summary.error_count || 0}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="价格更新"
                      value={selectedTask.summary.price_updated_count || 0}
                      valueStyle={{ color: '#1677ff' }}
                    />
                  </Col>
                </Row>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
