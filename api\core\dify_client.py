"""Dify API客户端封装"""

import json
import logging
import time
import asyncio
from typing import Dict, List, AsyncGenerator
import aiohttp
from tenacity import retry, wait_exponential, stop_after_attempt
from ..core.logging_config import get_logger
from ..config import DIFY_API_URL, DIFY_API_KEY

logger = get_logger("dify_client")

# 创建信号量限制并发连接数
_semaphore = asyncio.Semaphore(5)  # 最多同时处理5个请求

class DifyClient:
    """Dify API客户端"""
    
    @classmethod
    @retry(wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3))
    async def chat(
        cls,
        query: str,
        response_mode: str = "streaming",
        conversation_id: str = "",
        user: str = ""
    ) -> AsyncGenerator[str, None]:
        """与Dify API进行聊天交互
        
        Args:
            query: 用户查询内容
            response_mode: 响应模式，默认为"streaming"
            conversation_id: 会话ID，默认为空
            user: 用户标识，默认为空
            files: 文件列表，默认为空
            
        Yields:
            str: 流式响应的SSE格式数据块
        """
        # 使用信号量限制并发连接数
        async with _semaphore:
            try:
                # 准备请求URL和headers
                url = f"{DIFY_API_URL}/v1/chat-messages"
                headers = {
                    "Authorization": f"Bearer {DIFY_API_KEY}",
                    "Content-Type": "application/json"
                }
                
                # 准备请求体
                data = {
                    "inputs": {},
                    "query": query,
                    "response_mode": response_mode,
                    "conversation_id": conversation_id,
                    "user": user, 
                }
                
                # 记录请求信息
                start_time = time.time()
                cls._log_request("POST", url, headers, data)
                
                # 设置超时，避免长时间阻塞
                timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_connect=10, sock_read=30)
                
                # 发送请求
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    try:
                        async with session.post(url, json=data, headers=headers) as response:
                            if response.status != 200:
                                error_msg = f"API请求失败: HTTP {response.status}"
                                try:
                                    error_content = await response.text()
                                    error_msg += f", 响应内容: {error_content}"
                                except:
                                    pass
                                raise ValueError(error_msg)
                            
                            # 处理流式响应
                            buffer = ""
                            async for line in response.content:
                                if line:
                                    # 让出控制权，避免长时间阻塞事件循环
                                    await asyncio.sleep(0)
                                    
                                    decoded_line = line.decode("utf-8")
                                    # 跳过心跳包
                                    if decoded_line.strip() == "":
                                        continue
                                    
                                    # 处理可能的分块传输
                                    buffer += decoded_line
                                    if buffer.endswith("\n"):
                                        lines = buffer.splitlines()
                                        buffer = ""
                                        for line_content in lines:
                                            if not line_content.strip():
                                                continue
                                                
                                            # 检查是否是SSE数据
                                            if line_content.startswith("data: "):
                                                try:
                                                    # 去掉data前缀并解析JSON
                                                    json_str = line_content[6:].strip()
                                                    chunk = json.loads(json_str)
                                                    cls._log_stream_chunk(chunk)
                                                    # 将字典转换为SSE格式字符串
                                                    yield f"data: {json.dumps(chunk)}\n\n"
                                                except json.JSONDecodeError as e:
                                                    logger.error(f"解析流式响应失败: {e}, 原始数据: {line_content}")
                                                    error_chunk = {"error": str(e), "raw_data": line_content}
                                                    yield f"data: {json.dumps(error_chunk)}\n\n"
                                            else:
                                                # 非SSE数据，记录日志并跳过
                                                logger.debug(f"跳过非SSE数据: {line_content}")
                            
                            # 处理剩余的buffer
                            if buffer.strip():
                                if buffer.startswith("data: "):
                                    try:
                                        json_str = buffer[6:].strip()
                                        chunk = json.loads(json_str)
                                        cls._log_stream_chunk(chunk)
                                        yield f"data: {json.dumps(chunk)}\n\n"
                                    except json.JSONDecodeError as e:
                                        logger.error(f"解析流式响应失败: {e}, 原始数据: {buffer}")
                                        error_chunk = {"error": str(e), "raw_data": buffer}
                                        yield f"data: {json.dumps(error_chunk)}\n\n"
                    except asyncio.TimeoutError:
                        logger.error("Dify API请求超时")
                        error_chunk = {"error": "请求超时，请稍后重试"}
                        yield f"data: {json.dumps(error_chunk)}\n\n"
                    except Exception as e:
                        logger.error(f"处理Dify API响应时出错: {e}")
                        error_chunk = {"error": str(e)}
                        yield f"data: {json.dumps(error_chunk)}\n\n"
                        
                # 记录请求完成时间
                end_time = time.time()
                logger.debug(f"Dify API请求完成，耗时: {end_time - start_time:.2f}秒")
                        
            except Exception as e:
                logger.error(f"Dify API请求失败: {e}")
                error_chunk = {"error": str(e)}
                yield f"data: {json.dumps(error_chunk)}\n\n"
                # 不抛出异常，而是通过SSE返回错误信息

    @classmethod
    def _log_request(cls, method: str, url: str, headers: dict, data: dict):
        """记录API请求信息"""
        safe_headers = headers.copy()
        if 'Authorization' in safe_headers:
            safe_headers['Authorization'] = '*** REDACTED ***'
            
        logger.debug(f"Dify API请求 [{method}] {url}")
        logger.debug(f"请求头: {json.dumps(safe_headers, ensure_ascii=False, indent=2)}")
        logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

    @classmethod
    def _log_stream_chunk(cls, chunk: any):
        """记录流式响应的数据块"""
        try:
            safe_chunk = cls._sanitize_response_data(chunk)
            logger.debug(f"流式响应块: {json.dumps(safe_chunk, ensure_ascii=False)}")
        except Exception as e:
            logger.debug(f"流式响应块(无法序列化): {str(e)}")

    @classmethod
    def _sanitize_response_data(cls, data: any) -> any:
        """清理响应数据中的敏感信息"""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                if key.lower() in ['authorization', 'api_key', 'apikey', 'key', 'secret', 'password', 'token']:
                    result[key] = '*** REDACTED ***'
                else:
                    result[key] = cls._sanitize_response_data(value)
            return result
        elif isinstance(data, list):
            return [cls._sanitize_response_data(item) for item in data]
        else:
            return data
