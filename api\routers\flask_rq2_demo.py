"""
Flask-RQ2 演示路由
展示Flask-RQ2的使用方法
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

from ..core.debug import get_app_logger
from ..core.task_manager import (
    task_manager, submit_task, get_task_status, cancel_task,
    retry_task, get_queue_info, get_tasks_by_status, clear_queue
)
from ..tasks.example_flask_rq2_tasks import (
    example_task, high_priority_task, batch_processing_task,
    submit_example_tasks, check_task_status, manage_tasks_example,
    example_task_func, high_priority_task_func
)

# 获取日志记录器
logger = get_app_logger("flask_rq2_demo")

# 创建路由
router = APIRouter(
    prefix="/flask-rq2",
    tags=["flask-rq2"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class ExampleTaskRequest(BaseModel):
    """示例任务请求模型"""
    name: str
    delay: int = 5

class HighPriorityTaskRequest(BaseModel):
    """高优先级任务请求模型"""
    data: Dict[str, Any]

class BatchTaskRequest(BaseModel):
    """批处理任务请求模型"""
    items: List[str]

# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    message: str

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    id: str
    status: str
    result: Optional[Any] = None
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    ended_at: Optional[str] = None
    exc_info: Optional[str] = None
    meta: Optional[Dict[str, Any]] = None

@router.post("/example-task", response_model=TaskResponse)
async def submit_example_task(request: ExampleTaskRequest):
    """
    提交示例任务

    Args:
        request: 任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到示例任务请求: {request.name}")

    try:
        # 使用Flask-RQ2装饰器提交任务
        job = example_task.queue(request.name, request.delay)

        return TaskResponse(
            task_id=job.id,
            status="queued",
            message=f"示例任务已提交: {request.name}"
        )
    except Exception as e:
        logger.error(f"提交示例任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/high-priority-task", response_model=TaskResponse)
async def submit_high_priority_task(request: HighPriorityTaskRequest):
    """
    提交高优先级任务

    Args:
        request: 任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到高优先级任务请求")

    try:
        # 使用Flask-RQ2装饰器提交高优先级任务
        job = high_priority_task.queue(request.data)

        return TaskResponse(
            task_id=job.id,
            status="queued",
            message="高优先级任务已提交"
        )
    except Exception as e:
        logger.error(f"提交高优先级任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/batch-task", response_model=TaskResponse)
async def submit_batch_task(request: BatchTaskRequest):
    """
    提交批处理任务

    Args:
        request: 任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到批处理任务请求，项目数量: {len(request.items)}")

    try:
        # 使用Flask-RQ2装饰器提交批处理任务
        job = batch_processing_task.queue(request.items)

        return TaskResponse(
            task_id=job.id,
            status="queued",
            message=f"批处理任务已提交，项目数量: {len(request.items)}"
        )
    except Exception as e:
        logger.error(f"提交批处理任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/submit-all-examples", response_model=Dict[str, List[str]])
async def submit_all_example_tasks():
    """
    提交所有示例任务

    Returns:
        所有任务的ID列表
    """
    logger.info("提交所有示例任务")

    try:
        task_ids = submit_example_tasks()

        return {
            "message": "所有示例任务已提交",
            "task_ids": task_ids
        }
    except Exception as e:
        logger.error(f"提交示例任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.get("/task/{task_id}/status", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """
    获取任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    logger.info(f"查询任务状态: {task_id}")

    try:
        status_info = check_task_status(task_id)

        if status_info.get("status") == "not_found":
            raise HTTPException(status_code=404, detail="任务不存在")
        elif status_info.get("status") == "error":
            raise HTTPException(status_code=500, detail=status_info.get("message"))

        return TaskStatusResponse(**status_info)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询任务状态时出错: {str(e)}")

@router.get("/queues/info")
async def get_queues_info():
    """
    获取队列信息 - 使用任务管理器

    Returns:
        队列信息
    """
    logger.info("获取队列信息")

    try:
        queues_info = get_queue_info()

        return {
            "message": "队列信息获取成功",
            "queues": queues_info
        }
    except Exception as e:
        logger.error(f"获取队列信息时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取队列信息时出错: {str(e)}")

@router.delete("/task/{task_id}")
async def cancel_task_endpoint(task_id: str):
    """
    取消任务 - 使用任务管理器

    Args:
        task_id: 任务ID

    Returns:
        取消结果
    """
    logger.info(f"取消任务: {task_id}")

    try:
        result = cancel_task(task_id)

        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])

        return {
            "message": result["message"],
            "task_id": task_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消任务时出错: {str(e)}")

@router.get("/demo/info")
async def get_demo_info():
    """
    获取演示信息

    Returns:
        演示信息
    """
    return {
        "title": "Flask-RQ2 演示",
        "description": "这是一个Flask-RQ2的演示API，展示了如何使用Flask-RQ2进行任务队列管理",
        "features": [
            "使用装饰器定义任务",
            "支持多个队列（default, high, low）",
            "任务状态查询",
            "任务取消",
            "队列信息查询"
        ],
        "endpoints": [
            "POST /flask-rq2/example-task - 提交示例任务",
            "POST /flask-rq2/high-priority-task - 提交高优先级任务",
            "POST /flask-rq2/batch-task - 提交批处理任务",
            "POST /flask-rq2/submit-all-examples - 提交所有示例任务",
            "GET /flask-rq2/task/{task_id}/status - 获取任务状态",
            "GET /flask-rq2/queues/info - 获取队列信息",
            "DELETE /flask-rq2/task/{task_id} - 取消任务"
        ],
        "new_endpoints": [
            "POST /flask-rq2/submit-direct - 直接提交任务（使用任务管理器）",
            "POST /flask-rq2/task/{task_id}/retry - 重试失败的任务",
            "GET /flask-rq2/tasks/by-status/{status} - 根据状态获取任务列表",
            "DELETE /flask-rq2/queue/{queue_name}/clear - 清空队列",
            "GET /flask-rq2/manage-example - 任务管理示例"
        ]
    }

# 新增的任务管理器端点

class DirectTaskRequest(BaseModel):
    """直接任务提交请求模型"""
    function_name: str
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}
    queue: str = "default"
    timeout: int = 300
    description: str = None

@router.post("/submit-direct", response_model=TaskResponse)
async def submit_direct_task(request: DirectTaskRequest):
    """
    直接提交任务 - 使用任务管理器

    Args:
        request: 直接任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到直接任务提交请求: {request.function_name}")

    try:
        # 根据函数名选择任务函数
        if request.function_name == "example_task":
            func = example_task_func
        elif request.function_name == "high_priority_task":
            func = high_priority_task_func
        else:
            raise HTTPException(status_code=400, detail=f"未知的任务函数: {request.function_name}")

        # 使用任务管理器提交任务
        task_id = submit_task(
            func,
            *request.args,
            queue=request.queue,
            timeout=request.timeout,
            description=request.description,
            **request.kwargs
        )

        return TaskResponse(
            task_id=task_id,
            status="queued",
            message=f"任务已通过任务管理器提交: {request.function_name}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"直接提交任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/task/{task_id}/retry")
async def retry_task_endpoint(task_id: str):
    """
    重试失败的任务

    Args:
        task_id: 任务ID

    Returns:
        重试结果
    """
    logger.info(f"重试任务: {task_id}")

    try:
        result = retry_task(task_id)

        if not result["success"]:
            if "不存在" in result["message"]:
                raise HTTPException(status_code=404, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])

        return {
            "message": result["message"],
            "original_task_id": task_id,
            "new_task_id": result.get("new_task_id")
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"重试任务时出错: {str(e)}")

@router.get("/tasks/by-status/{status}")
async def get_tasks_by_status_endpoint(status: str, queue_name: str = "default", limit: int = 50):
    """
    根据状态获取任务列表

    Args:
        status: 任务状态 (queued, started, finished, failed, deferred)
        queue_name: 队列名称
        limit: 返回任务数量限制

    Returns:
        任务列表
    """
    logger.info(f"获取状态为 {status} 的任务列表")

    try:
        if status not in ["queued", "started", "finished", "failed", "deferred"]:
            raise HTTPException(status_code=400, detail="无效的任务状态")

        tasks = get_tasks_by_status(status, queue_name, limit)

        return {
            "message": f"获取状态为 {status} 的任务列表成功",
            "status": status,
            "queue_name": queue_name,
            "total_count": len(tasks),
            "tasks": tasks
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务列表时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务列表时出错: {str(e)}")

@router.delete("/queue/{queue_name}/clear")
async def clear_queue_endpoint(queue_name: str, status: str = "all"):
    """
    清空队列

    Args:
        queue_name: 队列名称
        status: 要清空的任务状态 (all, queued, finished, failed)

    Returns:
        清空结果
    """
    logger.info(f"清空队列 {queue_name}，状态: {status}")

    try:
        if queue_name not in ["default", "high", "low"]:
            raise HTTPException(status_code=400, detail="无效的队列名称")

        if status not in ["all", "queued", "finished", "failed"]:
            raise HTTPException(status_code=400, detail="无效的状态参数")

        result = clear_queue(queue_name, status)

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["message"])

        return {
            "message": result["message"],
            "queue_name": queue_name,
            "status": status,
            "cleared_count": result["cleared_count"]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空队列时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"清空队列时出错: {str(e)}")

@router.get("/manage-example")
async def get_manage_example():
    """
    任务管理示例

    Returns:
        任务管理示例结果
    """
    logger.info("执行任务管理示例")

    try:
        result = manage_tasks_example()

        return {
            "message": "任务管理示例执行成功",
            "example_result": result
        }
    except Exception as e:
        logger.error(f"执行任务管理示例时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"执行示例时出错: {str(e)}")
