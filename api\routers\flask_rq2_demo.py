"""
Flask-RQ2 演示路由
展示Flask-RQ2的使用方法
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

from ..core.debug import get_app_logger
from ..tasks.example_flask_rq2_tasks import (
    example_task, high_priority_task, batch_processing_task,
    submit_example_tasks, check_task_status
)
from ..core.rq_app import get_flask_rq, get_job

# 获取日志记录器
logger = get_app_logger("flask_rq2_demo")

# 创建路由
router = APIRouter(
    prefix="/flask-rq2",
    tags=["flask-rq2"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class ExampleTaskRequest(BaseModel):
    """示例任务请求模型"""
    name: str
    delay: int = 5

class HighPriorityTaskRequest(BaseModel):
    """高优先级任务请求模型"""
    data: Dict[str, Any]

class BatchTaskRequest(BaseModel):
    """批处理任务请求模型"""
    items: List[str]

# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    message: str

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    id: str
    status: str
    result: Optional[Any] = None
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    ended_at: Optional[str] = None
    exc_info: Optional[str] = None
    meta: Optional[Dict[str, Any]] = None

@router.post("/example-task", response_model=TaskResponse)
async def submit_example_task(request: ExampleTaskRequest):
    """
    提交示例任务
    
    Args:
        request: 任务请求
    
    Returns:
        任务响应
    """
    logger.info(f"收到示例任务请求: {request.name}")
    
    try:
        # 使用Flask-RQ2装饰器提交任务
        job = example_task.queue(request.name, request.delay)
        
        return TaskResponse(
            task_id=job.id,
            status="queued",
            message=f"示例任务已提交: {request.name}"
        )
    except Exception as e:
        logger.error(f"提交示例任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/high-priority-task", response_model=TaskResponse)
async def submit_high_priority_task(request: HighPriorityTaskRequest):
    """
    提交高优先级任务
    
    Args:
        request: 任务请求
    
    Returns:
        任务响应
    """
    logger.info(f"收到高优先级任务请求")
    
    try:
        # 使用Flask-RQ2装饰器提交高优先级任务
        job = high_priority_task.queue(request.data)
        
        return TaskResponse(
            task_id=job.id,
            status="queued",
            message="高优先级任务已提交"
        )
    except Exception as e:
        logger.error(f"提交高优先级任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/batch-task", response_model=TaskResponse)
async def submit_batch_task(request: BatchTaskRequest):
    """
    提交批处理任务
    
    Args:
        request: 任务请求
    
    Returns:
        任务响应
    """
    logger.info(f"收到批处理任务请求，项目数量: {len(request.items)}")
    
    try:
        # 使用Flask-RQ2装饰器提交批处理任务
        job = batch_processing_task.queue(request.items)
        
        return TaskResponse(
            task_id=job.id,
            status="queued",
            message=f"批处理任务已提交，项目数量: {len(request.items)}"
        )
    except Exception as e:
        logger.error(f"提交批处理任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.post("/submit-all-examples", response_model=Dict[str, List[str]])
async def submit_all_example_tasks():
    """
    提交所有示例任务
    
    Returns:
        所有任务的ID列表
    """
    logger.info("提交所有示例任务")
    
    try:
        task_ids = submit_example_tasks()
        
        return {
            "message": "所有示例任务已提交",
            "task_ids": task_ids
        }
    except Exception as e:
        logger.error(f"提交示例任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.get("/task/{task_id}/status", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
    
    Returns:
        任务状态信息
    """
    logger.info(f"查询任务状态: {task_id}")
    
    try:
        status_info = check_task_status(task_id)
        
        if status_info.get("status") == "not_found":
            raise HTTPException(status_code=404, detail="任务不存在")
        elif status_info.get("status") == "error":
            raise HTTPException(status_code=500, detail=status_info.get("message"))
        
        return TaskStatusResponse(**status_info)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"查询任务状态时出错: {str(e)}")

@router.get("/queues/info")
async def get_queues_info():
    """
    获取队列信息
    
    Returns:
        队列信息
    """
    logger.info("获取队列信息")
    
    try:
        rq = get_flask_rq()
        
        queues_info = {}
        for queue_name in ['default', 'high', 'low']:
            queue = rq.get_queue(queue_name)
            queues_info[queue_name] = {
                "name": queue.name,
                "length": len(queue),
                "is_empty": queue.is_empty(),
                "job_ids": queue.job_ids
            }
        
        return {
            "message": "队列信息获取成功",
            "queues": queues_info
        }
    except Exception as e:
        logger.error(f"获取队列信息时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取队列信息时出错: {str(e)}")

@router.delete("/task/{task_id}")
async def cancel_task(task_id: str):
    """
    取消任务
    
    Args:
        task_id: 任务ID
    
    Returns:
        取消结果
    """
    logger.info(f"取消任务: {task_id}")
    
    try:
        job = get_job(task_id)
        if job is None:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 取消任务
        job.cancel()
        
        return {
            "message": f"任务 {task_id} 已取消",
            "task_id": task_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消任务时出错: {str(e)}")

@router.get("/demo/info")
async def get_demo_info():
    """
    获取演示信息
    
    Returns:
        演示信息
    """
    return {
        "title": "Flask-RQ2 演示",
        "description": "这是一个Flask-RQ2的演示API，展示了如何使用Flask-RQ2进行任务队列管理",
        "features": [
            "使用装饰器定义任务",
            "支持多个队列（default, high, low）",
            "任务状态查询",
            "任务取消",
            "队列信息查询"
        ],
        "endpoints": [
            "POST /flask-rq2/example-task - 提交示例任务",
            "POST /flask-rq2/high-priority-task - 提交高优先级任务",
            "POST /flask-rq2/batch-task - 提交批处理任务",
            "POST /flask-rq2/submit-all-examples - 提交所有示例任务",
            "GET /flask-rq2/task/{task_id}/status - 获取任务状态",
            "GET /flask-rq2/queues/info - 获取队列信息",
            "DELETE /flask-rq2/task/{task_id} - 取消任务"
        ]
    }
