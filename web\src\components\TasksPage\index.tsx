import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Tabs,
  message,
  Tag,
  Space,
  Modal,
  Form,
  Select,
  Input,
  Descriptions,
  Tooltip,
  Badge,
  Typography,
  Progress,
  Row,
  Col,
  Statistic,
  Alert
} from 'antd';
import {
  PlayCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DashboardOutlined,
  LinkOutlined,
  FileExcelOutlined,
  DownloadOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { runTask, getTaskStatus, getActiveTasks, getCompletedTasks, cancelTask } from '../../api/tasks';
import axios from 'axios';

const { TabPane } = Tabs;
const { Option } = Select;
const { Link } = Typography;

// 任务状态对应的标签
const statusTags: Record<string, JSX.Element> = {
  // RQ任务状态
  PENDING: <Tag icon={<ClockCircleOutlined />} color="default">等待中</Tag>,
  STARTED: <Tag icon={<SyncOutlined spin />} color="processing">执行中</Tag>,
  SUCCESS: <Tag icon={<CheckCircleOutlined />} color="success">成功</Tag>,
  FAILURE: <Tag icon={<CloseCircleOutlined />} color="error">失败</Tag>,
  REVOKED: <Tag icon={<ExclamationCircleOutlined />} color="warning">已取消</Tag>,
  RETRY: <Tag icon={<SyncOutlined spin />} color="warning">重试中</Tag>,

  // Excel材料处理任务状态
  submitted: <Tag icon={<ClockCircleOutlined />} color="blue">已提交</Tag>,
  processing: <Tag icon={<SyncOutlined spin />} color="orange">处理中</Tag>,
  ready_for_merge: <Tag icon={<ExclamationCircleOutlined />} color="cyan">待合并</Tag>,
  completed: <Tag icon={<CheckCircleOutlined />} color="green">已完成</Tag>,
  partial_success: <Tag icon={<ExclamationCircleOutlined />} color="yellow">部分成功</Tag>,
  failed: <Tag icon={<CloseCircleOutlined />} color="red">失败</Tag>,
  merge_failed: <Tag icon={<CloseCircleOutlined />} color="red">合并失败</Tag>,
};

// 任务类型选项
const taskTypes = [
  { value: 'sync_materials', label: '材料数据同步' },
  { value: 'process_material', label: '材料数据处理' },
  { value: 'excel_material', label: 'Excel材料处理' },
];

const TasksPage: React.FC = () => {
  // 状态
  const [activeTab, setActiveTab] = useState<string>('rq-active');
  const [activeTasks, setActiveTasks] = useState<any[]>([]);
  const [completedTasks, setCompletedTasks] = useState<any[]>([]);
  const [excelTasks, setExcelTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [taskModalVisible, setTaskModalVisible] = useState<boolean>(false);
  const [taskForm] = Form.useForm();
  const [taskDetailModalVisible, setTaskDetailModalVisible] = useState<boolean>(false);
  const [currentTaskDetail, setCurrentTaskDetail] = useState<any>(null);

  // 加载Excel材料处理任务
  const loadExcelTasks = async () => {
    try {
      const response = await axios.get('/api/excel-material/tasks?limit=50');
      if (response.data && response.data.tasks) {
        setExcelTasks(response.data.tasks);
      }
    } catch (error) {
      console.error('加载Excel任务失败:', error);
    }
  };

  // 加载任务数据
  const loadTasks = async () => {
    setLoading(true);
    try {
      if (activeTab === 'rq-active') {
        const response = await getActiveTasks();
        if (response.data) {
          setActiveTasks(response.data);
        }
      } else if (activeTab === 'rq-completed') {
        const response = await getCompletedTasks(20);
        if (response.data) {
          setCompletedTasks(response.data);
        }
      } else if (activeTab === 'excel-tasks') {
        await loadExcelTasks();
      }
    } catch (error) {
      console.error('加载任务失败:', error);
      message.error('加载任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新任务状态
  const refreshTaskStatus = async (taskId: string) => {
    try {
      const response = await getTaskStatus(taskId);
      if (response.data) {
        // 更新活跃任务列表中的任务状态
        setActiveTasks(prev =>
          prev.map(task =>
            task.task_id === taskId ? { ...task, ...response.data } : task
          )
        );

        // 如果任务已完成，从活跃列表移除并添加到已完成列表
        if (['SUCCESS', 'FAILURE', 'REVOKED'].includes(response.data.status)) {
          setActiveTasks(prev => prev.filter(task => task.task_id !== taskId));
          setCompletedTasks(prev => [{ ...response.data, task_id: taskId }, ...prev]);
        }
      }
    } catch (error) {
      console.error(`刷新任务状态失败 (${taskId}):`, error);
    }
  };

  // 取消任务
  const handleCancelTask = async (taskId: string) => {
    try {
      await cancelTask(taskId);
      message.success('任务已取消');
      loadTasks();
    } catch (error) {
      console.error('取消任务失败:', error);
      message.error('取消任务失败');
    }
  };

  // 合并Excel任务结果
  const handleMergeExcelTask = async (taskId: string) => {
    try {
      await axios.post(`/api/excel-material/task/${taskId}/merge`);
      message.success('结果合并任务已启动');
      loadTasks();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '合并失败');
    }
  };

  // 下载Excel任务结果
  const handleDownloadExcelResult = async (taskId: string) => {
    try {
      const response = await axios.get(`/api/excel-material/task/${taskId}/download`, {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `材料处理结果_${taskId}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success('文件下载成功');
    } catch (error: any) {
      message.error(error.response?.data?.detail || '下载失败');
    }
  };

  // 删除Excel任务
  const handleDeleteExcelTask = async (taskId: string) => {
    try {
      await axios.delete(`/api/excel-material/task/${taskId}`);
      message.success('任务已删除');
      loadTasks();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '删除失败');
    }
  };

  // 查看Excel任务详情
  const viewExcelTaskDetail = async (taskId: string) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/excel-material/task/${taskId}/status`);
      if (response.data) {
        setCurrentTaskDetail({
          ...response.data,
          task_type: 'excel_material',
          task_name: 'Excel材料处理'
        });
        setTaskDetailModalVisible(true);
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '获取任务详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看任务详情
  const viewTaskDetail = async (taskId: string) => {
    try {
      setLoading(true);
      const response = await getTaskStatus(taskId);
      if (response.data) {
        setCurrentTaskDetail(response.data);
        setTaskDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      message.error('获取任务详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交新任务
  const handleSubmitTask = async () => {
    try {
      const values = await taskForm.validateFields();
      const { taskType, materialId } = values;

      let params = {};
      if (taskType === 'process_material' && materialId) {
        params = { material_id: materialId };
      }

      const response = await runTask(taskType, Object.keys(params).length > 0 ? params : undefined);
      if (response.data) {
        message.success('任务已提交');
        setTaskModalVisible(false);
        taskForm.resetFields();

        // 刷新任务列表
        loadTasks();
      }
    } catch (error) {
      console.error('提交任务失败:', error);
      message.error('提交任务失败');
    }
  };

  // 设置定时刷新
  useEffect(() => {
    loadTasks();

    // 设置10秒刷新一次
    const interval = setInterval(() => {
      if (activeTab === 'rq-active' || activeTab === 'excel-tasks') {
        loadTasks();
      }
    }, 10000);

    setRefreshInterval(interval);

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [activeTab]);

  // Excel材料处理任务表格列配置
  const excelTaskColumns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 200,
      render: (text: string) => (
        <Typography.Text code copyable={{ text }}>
          {text.substring(0, 16)}...
        </Typography.Text>
      ),
    },
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => statusTags[status] || <Tag>{status}</Tag>,
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number, record: any) => (
        <Progress
          percent={Math.round(progress || 0)}
          size="small"
          status={record.status === 'failed' ? 'exception' : 'active'}
        />
      ),
    },
    {
      title: '数据行数',
      dataIndex: 'total_rows',
      key: 'total_rows',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (text: string) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => viewExcelTaskDetail(record.task_id)}
            />
          </Tooltip>

          {record.status === 'ready_for_merge' && (
            <Tooltip title="合并结果">
              <Button
                type="text"
                icon={<SyncOutlined />}
                onClick={() => handleMergeExcelTask(record.task_id)}
              />
            </Tooltip>
          )}

          {record.status === 'completed' && (
            <Tooltip title="下载结果">
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadExcelResult(record.task_id)}
              />
            </Tooltip>
          )}

          <Tooltip title="删除任务">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteExcelTask(record.task_id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // RQ任务表格列配置
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 280,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '任务类型',
      dataIndex: 'task_name',
      key: 'task_name',
      render: (text: string) => {
        const taskName = text?.split('.')?.pop() || text;
        const taskType = taskTypes.find(t => taskName?.includes(t.value));
        return taskType?.label || taskName;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => statusTags[status] || <Tag>{status}</Tag>
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => viewTaskDetail(record.task_id)}
          >
            详情
          </Button>
          {activeTab === 'rq-active' && record.status !== 'REVOKED' && (
            <Button
              type="link"
              danger
              size="small"
              onClick={() => handleCancelTask(record.task_id)}
            >
              取消
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16 }}>
        <Card style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
            <DashboardOutlined style={{ fontSize: 20, marginRight: 8 }} />
            <span style={{ fontWeight: 'bold' }}>RQ监控工具: </span>
            <Link
              href={`${window.location.protocol}//${window.location.hostname}:9181`}
              target="_blank"
              style={{ marginLeft: 8 }}
            >
              访问RQ Dashboard监控界面 <LinkOutlined />
            </Link>
          </div>
        </Card>
      </div>

      <Card
        title="任务管理"
        bordered={false}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setTaskModalVisible(true)}
            >
              新建任务
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={loadTasks}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
        >
          <TabPane
            tab={
              <span>
                <Badge count={activeTasks.length} offset={[10, 0]}>
                  RQ活跃任务
                </Badge>
              </span>
            }
            key="rq-active"
          >
            <Table
              dataSource={activeTasks}
              columns={columns}
              rowKey="task_id"
              loading={loading && activeTab === 'rq-active'}
              pagination={false}
            />
          </TabPane>
          <TabPane
            tab="RQ已完成任务"
            key="rq-completed"
          >
            <Table
              dataSource={completedTasks}
              columns={columns}
              rowKey="task_id"
              loading={loading && activeTab === 'rq-completed'}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <Badge count={excelTasks.filter(t => ['submitted', 'processing', 'ready_for_merge'].includes(t.status)).length} offset={[10, 0]}>
                  <FileExcelOutlined /> Excel材料处理
                </Badge>
              </span>
            }
            key="excel-tasks"
          >
            {excelTasks.length === 0 ? (
              <Alert
                message="暂无Excel材料处理任务"
                description="点击右上角新建任务按钮或前往Excel材料处理页面开始上传文件。"
                type="info"
                showIcon
                style={{ margin: '20px 0' }}
                action={
                  <Button
                    type="primary"
                    icon={<FileExcelOutlined />}
                    onClick={() => window.open('/excel-material', '_blank')}
                  >
                    前往Excel处理页面
                  </Button>
                }
              />
            ) : (
              <Table
                dataSource={excelTasks}
                columns={excelTaskColumns}
                rowKey="task_id"
                loading={loading && activeTab === 'excel-tasks'}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 个任务`,
                }}
              />
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 新建任务对话框 */}
      <Modal
        title="新建任务"
        open={taskModalVisible}
        onOk={handleSubmitTask}
        onCancel={() => setTaskModalVisible(false)}
      >
        <Form form={taskForm} layout="vertical">
          <Form.Item
            name="taskType"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="选择任务类型">
              {taskTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.taskType !== currentValues.taskType
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('taskType') === 'process_material' ? (
                <Form.Item
                  name="materialId"
                  label="材料ID"
                  rules={[{ required: true, message: '请输入材料ID' }]}
                >
                  <Input placeholder="输入材料ID" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
        </Form>
      </Modal>

      {/* 任务详情对话框 */}
      <Modal
        title="任务详情"
        open={taskDetailModalVisible}
        onCancel={() => setTaskDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setTaskDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentTaskDetail && (
          <div>
            {/* Excel材料处理任务详情 */}
            {currentTaskDetail.task_type === 'excel_material' ? (
              <div>
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={8}>
                    <Statistic
                      title="总批次"
                      value={currentTaskDetail.total_batches || 0}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="已完成"
                      value={currentTaskDetail.completed_batches || 0}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="失败批次"
                      value={currentTaskDetail.failed_batches || 0}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                </Row>

                <Descriptions bordered column={2}>
                  <Descriptions.Item label="任务ID">
                    <Typography.Text code copyable>
                      {currentTaskDetail.task_id}
                    </Typography.Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    {statusTags[currentTaskDetail.status] || currentTaskDetail.status}
                  </Descriptions.Item>
                  <Descriptions.Item label="文件名">
                    {currentTaskDetail.filename || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="数据行数">
                    {currentTaskDetail.total_rows || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="创建时间">
                    {currentTaskDetail.created_at ? new Date(currentTaskDetail.created_at).toLocaleString() : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="过期时间">
                    {currentTaskDetail.expires_at ? new Date(currentTaskDetail.expires_at).toLocaleString() : '-'}
                  </Descriptions.Item>
                </Descriptions>

                {currentTaskDetail.summary && (
                  <div style={{ marginTop: 16 }}>
                    <Typography.Title level={5}>处理统计</Typography.Title>
                    <Row gutter={16}>
                      <Col span={6}>
                        <Statistic
                          title="总处理数"
                          value={currentTaskDetail.summary.total_processed || 0}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="成功数"
                          value={currentTaskDetail.summary.success_count || 0}
                          valueStyle={{ color: '#3f8600' }}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="失败数"
                          value={currentTaskDetail.summary.error_count || 0}
                          valueStyle={{ color: '#cf1322' }}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="价格更新"
                          value={currentTaskDetail.summary.price_updated_count || 0}
                          valueStyle={{ color: '#1677ff' }}
                        />
                      </Col>
                    </Row>
                  </div>
                )}
              </div>
            ) : (
              /* RQ任务详情 */
              <Descriptions bordered column={2}>
                <Descriptions.Item label="任务ID" span={2}>
                  {currentTaskDetail.task_id}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {statusTags[currentTaskDetail.status] || currentTaskDetail.status}
                </Descriptions.Item>
                <Descriptions.Item label="任务名称">
                  {currentTaskDetail.task_name}
                </Descriptions.Item>
                {currentTaskDetail.result && (
                  <Descriptions.Item label="结果" span={2}>
                    <pre style={{ maxHeight: 300, overflow: 'auto' }}>
                      {JSON.stringify(currentTaskDetail.result, null, 2)}
                    </pre>
                  </Descriptions.Item>
                )}
                {currentTaskDetail.error && (
                  <Descriptions.Item label="错误信息" span={2}>
                    <pre style={{ maxHeight: 300, overflow: 'auto', color: 'red' }}>
                      {currentTaskDetail.error}
                    </pre>
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TasksPage;
