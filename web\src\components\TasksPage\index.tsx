import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Tabs,
  message,
  Tag,
  Space,
  Modal,
  Form,
  Select,
  Input,
  Descriptions,
  Tooltip,
  Badge,
  Typography
} from 'antd';
import {
  PlayCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DashboardOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { runTask, getTaskStatus, getActiveTasks, getCompletedTasks, cancelTask } from '../../api/tasks';

const { TabPane } = Tabs;
const { Option } = Select;
const { Link } = Typography;

// 任务状态对应的标签
const statusTags: Record<string, JSX.Element> = {
  PENDING: <Tag icon={<ClockCircleOutlined />} color="default">等待中</Tag>,
  STARTED: <Tag icon={<SyncOutlined spin />} color="processing">执行中</Tag>,
  SUCCESS: <Tag icon={<CheckCircleOutlined />} color="success">成功</Tag>,
  FAILURE: <Tag icon={<CloseCircleOutlined />} color="error">失败</Tag>,
  REVOKED: <Tag icon={<ExclamationCircleOutlined />} color="warning">已取消</Tag>,
  RETRY: <Tag icon={<SyncOutlined spin />} color="warning">重试中</Tag>,
};

// 任务类型选项
const taskTypes = [
  { value: 'sync_materials', label: '材料数据同步' },
  { value: 'process_material', label: '材料数据处理' },
];

const TasksPage: React.FC = () => {
  // 状态
  const [activeTab, setActiveTab] = useState<string>('active');
  const [activeTasks, setActiveTasks] = useState<any[]>([]);
  const [completedTasks, setCompletedTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [taskModalVisible, setTaskModalVisible] = useState<boolean>(false);
  const [taskForm] = Form.useForm();
  const [taskDetailModalVisible, setTaskDetailModalVisible] = useState<boolean>(false);
  const [currentTaskDetail, setCurrentTaskDetail] = useState<any>(null);

  // 加载任务数据
  const loadTasks = async () => {
    setLoading(true);
    try {
      if (activeTab === 'active') {
        const response = await getActiveTasks();
        if (response.data) {
          setActiveTasks(response.data);
        }
      } else {
        const response = await getCompletedTasks(20);
        if (response.data) {
          setCompletedTasks(response.data);
        }
      }
    } catch (error) {
      console.error('加载任务失败:', error);
      message.error('加载任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新任务状态
  const refreshTaskStatus = async (taskId: string) => {
    try {
      const response = await getTaskStatus(taskId);
      if (response.data) {
        // 更新活跃任务列表中的任务状态
        setActiveTasks(prev =>
          prev.map(task =>
            task.task_id === taskId ? { ...task, ...response.data } : task
          )
        );

        // 如果任务已完成，从活跃列表移除并添加到已完成列表
        if (['SUCCESS', 'FAILURE', 'REVOKED'].includes(response.data.status)) {
          setActiveTasks(prev => prev.filter(task => task.task_id !== taskId));
          setCompletedTasks(prev => [{ ...response.data, task_id: taskId }, ...prev]);
        }
      }
    } catch (error) {
      console.error(`刷新任务状态失败 (${taskId}):`, error);
    }
  };

  // 取消任务
  const handleCancelTask = async (taskId: string) => {
    try {
      await cancelTask(taskId);
      message.success('任务已取消');
      loadTasks();
    } catch (error) {
      console.error('取消任务失败:', error);
      message.error('取消任务失败');
    }
  };

  // 查看任务详情
  const viewTaskDetail = async (taskId: string) => {
    try {
      setLoading(true);
      const response = await getTaskStatus(taskId);
      if (response.data) {
        setCurrentTaskDetail(response.data);
        setTaskDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      message.error('获取任务详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交新任务
  const handleSubmitTask = async () => {
    try {
      const values = await taskForm.validateFields();
      const { taskType, materialId } = values;

      let params = {};
      if (taskType === 'process_material' && materialId) {
        params = { material_id: materialId };
      }

      const response = await runTask(taskType, Object.keys(params).length > 0 ? params : undefined);
      if (response.data) {
        message.success('任务已提交');
        setTaskModalVisible(false);
        taskForm.resetFields();

        // 刷新任务列表
        loadTasks();
      }
    } catch (error) {
      console.error('提交任务失败:', error);
      message.error('提交任务失败');
    }
  };

  // 设置定时刷新
  useEffect(() => {
    loadTasks();

    // 设置10秒刷新一次
    const interval = setInterval(() => {
      if (activeTab === 'active') {
        loadTasks();
      }
    }, 10000);

    setRefreshInterval(interval);

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [activeTab]);

  // 表格列配置
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 280,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '任务类型',
      dataIndex: 'task_name',
      key: 'task_name',
      render: (text: string) => {
        const taskName = text?.split('.')?.pop() || text;
        const taskType = taskTypes.find(t => taskName?.includes(t.value));
        return taskType?.label || taskName;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => statusTags[status] || <Tag>{status}</Tag>
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => viewTaskDetail(record.task_id)}
          >
            详情
          </Button>
          {activeTab === 'active' && record.status !== 'REVOKED' && (
            <Button
              type="link"
              danger
              size="small"
              onClick={() => handleCancelTask(record.task_id)}
            >
              取消
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16 }}>
        <Card style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
            <DashboardOutlined style={{ fontSize: 20, marginRight: 8 }} />
            <span style={{ fontWeight: 'bold' }}>RQ监控工具: </span>
            <Link
              href={`${window.location.protocol}//${window.location.hostname}:9181`}
              target="_blank"
              style={{ marginLeft: 8 }}
            >
              访问RQ Dashboard监控界面 <LinkOutlined />
            </Link>
          </div>
        </Card>
      </div>

      <Card
        title="任务管理"
        bordered={false}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setTaskModalVisible(true)}
            >
              新建任务
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={loadTasks}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
        >
          <TabPane
            tab={
              <span>
                <Badge count={activeTasks.length} offset={[10, 0]}>
                  活跃任务
                </Badge>
              </span>
            }
            key="active"
          >
            <Table
              dataSource={activeTasks}
              columns={columns}
              rowKey="task_id"
              loading={loading && activeTab === 'active'}
              pagination={false}
            />
          </TabPane>
          <TabPane
            tab="已完成任务"
            key="completed"
          >
            <Table
              dataSource={completedTasks}
              columns={columns}
              rowKey="task_id"
              loading={loading && activeTab === 'completed'}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 新建任务对话框 */}
      <Modal
        title="新建任务"
        open={taskModalVisible}
        onOk={handleSubmitTask}
        onCancel={() => setTaskModalVisible(false)}
      >
        <Form form={taskForm} layout="vertical">
          <Form.Item
            name="taskType"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="选择任务类型">
              {taskTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.taskType !== currentValues.taskType
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('taskType') === 'process_material' ? (
                <Form.Item
                  name="materialId"
                  label="材料ID"
                  rules={[{ required: true, message: '请输入材料ID' }]}
                >
                  <Input placeholder="输入材料ID" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
        </Form>
      </Modal>

      {/* 任务详情对话框 */}
      <Modal
        title="任务详情"
        open={taskDetailModalVisible}
        onCancel={() => setTaskDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setTaskDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {currentTaskDetail && (
          <Descriptions bordered column={2}>
            <Descriptions.Item label="任务ID" span={2}>
              {currentTaskDetail.task_id}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {statusTags[currentTaskDetail.status] || currentTaskDetail.status}
            </Descriptions.Item>
            <Descriptions.Item label="任务名称">
              {currentTaskDetail.task_name}
            </Descriptions.Item>
            {currentTaskDetail.result && (
              <Descriptions.Item label="结果" span={2}>
                <pre style={{ maxHeight: 300, overflow: 'auto' }}>
                  {JSON.stringify(currentTaskDetail.result, null, 2)}
                </pre>
              </Descriptions.Item>
            )}
            {currentTaskDetail.error && (
              <Descriptions.Item label="错误信息" span={2}>
                <pre style={{ maxHeight: 300, overflow: 'auto', color: 'red' }}>
                  {currentTaskDetail.error}
                </pre>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default TasksPage;
