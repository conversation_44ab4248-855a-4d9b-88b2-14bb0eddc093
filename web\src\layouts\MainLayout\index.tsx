import { Link, Outlet, useLocation } from 'react-router-dom';
import ProLayout from '@ant-design/pro-layout';
// 导入一个合适的图标，例如 ExperimentOutlined 或 BulbOutlined
import {
  HomeOutlined,
  UploadOutlined,
  SearchOutlined,
  ExperimentOutlined,
  SyncOutlined,
  ScheduleOutlined
} from '@ant-design/icons';
import './style.css';
import React, { Suspense } from 'react';

export default function MainLayout() {
  const location = useLocation();

  return (
    <ProLayout
      title="物料价格管理系统"
      style={{ height: '100vh' }}
      className="custom-pro-layout"
      location={location}
      route={{
        path: '/',
        routes: [
          { path: '/', name: '首页', icon: <HomeOutlined /> },
          { path: '/import', name: '数据导入', icon: <UploadOutlined /> },
          { path: '/query', name: '数据查询', icon: <SearchOutlined /> },
          { path: '/vector-search', name: '向量检索', icon: <ExperimentOutlined /> }, // 添加向量检索菜单项
          { path: '/chat', name: '语义检索', icon: <SearchOutlined /> },
          { path: '/price-chat', name: '价格咨询', icon: <SearchOutlined /> },
          { path: '/sync', name: '数据同步', icon: <SyncOutlined /> },
          { path: '/tasks', name: '任务管理', icon: <ScheduleOutlined /> },
        ]
      }}
      menuItemRender={(item, dom) => <Link to={item.path!}>{dom}</Link>}
      logo="https://www.shecc.com:8443/static/logo/5.png"

    >
      <div className="content-wrapper">
        <Suspense fallback={<div style={{ padding: '24px', background: '#fff', minHeight: '280px' }}>加载中...</div>}>
          <Outlet />
        </Suspense>
      </div>
    </ProLayout>
  );
}
