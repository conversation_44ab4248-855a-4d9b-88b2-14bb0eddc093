from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import insert, inspect
import pandas as pd
import numpy as np  
import io
import json
from datetime import datetime
from .. import crud, schemas
from ..core.database import get_db
from ..models.item import Materials
from ..core.debug import get_app_logger

# 配置日志记录器
logger = get_app_logger(__name__)

router = APIRouter(
    prefix="/upload",
    tags=["upload"],
)

# 默认字段映射配置
DEFAULT_FIELD_MAPPING = {
    '产品名称': 'product_name',
    '设备名称': 'product_name',
    '物料名称': 'product_name',
    '名称': 'product_name',
    '规格型号': 'specification',
    '规格': 'specification',
    '型号': 'specification',
    '产品描述': 'description',
    '描述': 'description',
    '说明': 'description',
    '品牌': 'manufacturer',
    '厂家': 'manufacturer',
    '生产厂家': 'manufacturer',
    '制造商': 'manufacturer',
    '产地': 'origin',
    '原产地': 'origin',
    '数量': 'quantity',
    '单位': 'unit',
    '单价': 'unit_price',
    '价格': 'unit_price',
    '金额': 'total_price',
    '总价': 'total_price',
    '合计': 'total_price',
    '备注': 'remark',
    '说明': 'remark',
    '注释': 'remark',
    '送货安装': 'includes_delivery_installation',
    '包含安装': 'includes_delivery_installation',
    '送装服务': 'includes_delivery_installation',
    '安装服务': 'includes_delivery_installation',
    '是否送装': 'includes_delivery_installation',
    '保修月份': 'warranty_months',
    '保修期': 'warranty_months',
    '质保期': 'warranty_months',
    '质量保修': 'warranty_months',
    '保修时间': 'warranty_months'
}

# 获取数据库模型的所有字段
def get_model_fields():
    return [column.name for column in inspect(Materials).columns if column.name != 'id']
def process_excel_data(df: pd.DataFrame):
    # 清洗非法浮点数值
    df = df.replace([np.inf, -np.inf], np.nan)
    df = df.fillna('')
    
    # 转换数值类型为Python原生类型
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    df[numeric_cols] = df[numeric_cols].astype(object).where(df[numeric_cols].notnull(), None)
    
    return df

def find_end_row(df: pd.DataFrame) -> int:
    """
    从文件末尾开始查找结束行
    1. 去除结尾空行
    2. 向上查找，直到找到符合条件的行
    """
    # 去除结尾空行
    while len(df) > 0 and df.iloc[-1].isnull().all():
        df = df.iloc[:-1]
    
    # 从末尾开始查找结束行
    end_row = len(df) - 1
    while end_row >= 0:
        # 如果某行少于2个有效列，继续向上查找
        if df.iloc[end_row].count() < 2:
            end_row -= 1
        else:
            break
    
    return end_row


@router.post("/preview")
async def preview_excel(
    file: UploadFile = File(...),
    header_row: int = Form(0, gt=-1, le=10),  # Add validation
    db: Session = Depends(get_db)
):
    try:
        # 读取Excel文件
        contents = await file.read()
        # 不使用header参数读取Excel,保留所有列数据
        df = pd.read_excel(io.BytesIO(contents), header=None)
        df = process_excel_data(df)
        logger.info(f"收到预览请求 文件名: {file.filename} 大小: {len(contents)}字节")
          
        # 获取可能的标题行
        possible_headers = []
        for i in range(min(15, len(df))):  # 预览前15行
            possible_headers.append({
                "row_index": i,
                "content": df.iloc[i].tolist()
            })
        
        # 获取所有列名(使用数字索引)
        headers = [str(i) for i in range(len(df.columns))]
        
        # 检测结束行
        end_row = find_end_row(df)
        
        # 获取结尾20行数据
        end_data = []
        start_row = max(end_row - 19, 0)
        for i in range(start_row, end_row + 1):
            row_data = {}
            for j, header in enumerate(headers):
                if j < len(df.columns):
                    row_data[header] = df.iloc[i, j]
            end_data.append(row_data)
        
        # 预览数据（前5行）
        preview_data = []
        for i in range(min(5, len(df))):
            row_data = {}
            for j, header in enumerate(headers):
                if j < len(df.columns):
                    row_data[header] = df.iloc[i, j]
            preview_data.append(row_data)
        
        return {
            "possible_headers": possible_headers,
            "selected_header_row": header_row,
            "headers": headers,
            "preview_data": preview_data,
            "end_row": end_row,
            "end_data": end_data
        }
    
    except Exception as e:
        logger.error("预览文件失败", exc_info=e)
        raise HTTPException(
            status_code=500,
            detail={"error": str(e), "filename": file.filename}
        )
@router.post("/")
async def upload_excel(
    file: UploadFile = File(...),
    header_row: int = Form(1),  # 默认第二行为标题行(索引从0开始)
    end_row: int = Form(0),  # 默认结束行为0,表示倒数第一行
    field_mapping: str = Form(...),  # JSON字符串形式的字段映射
    db: Session = Depends(get_db)
):
    try:
        # 解析字段映射
        mapping = json.loads(field_mapping)
        logger.info(f"收到上传请求 文件名: {file.filename} 标题行: {header_row} 字段映射: {mapping}")
        
        # 读取Excel文件
        contents = await file.read()
        
        # 使用指定的标题行读取Excel
        df = pd.read_excel(io.BytesIO(contents), header=header_row)
        
        # 检测实际结束行
        actual_end_row = find_end_row(df)
        
        # 根据倒数行数过滤数据
        if end_row > 0:
            # 计算实际的结束位置(从倒数第end_row行开始截取)
            start_row = max(actual_end_row - 19, 0)  # 倒数20行的起始位置
            actual_row = start_row + end_row - 1  # 在倒数20行中的第end_row行
            df = df.iloc[:actual_row + 1]
        
        # 记录数据处理日志
        logger.info(f"解析到{len(df)}行数据 包含列: {df.columns.tolist()}")
        
        # 过滤掉合计行等非数据行
        if '序号' in df.columns:
            df = df.dropna(subset=['序号'])
            df = df[pd.to_numeric(df['序号'], errors='coerce').notnull()]
        
        # 转换数据为字典列表，并应用字段映射
        records = []
        for record in df.to_dict('records'):
            mapped_record = {}
            for excel_col, value in record.items():
                # 应用用户提供的字段映射
                if excel_col in mapping and mapping[excel_col]:
                    db_col = mapping[excel_col]
                    if hasattr(Materials, db_col):
                        mapped_record[db_col] = value
            
            if mapped_record:  # 过滤空记录
                records.append(mapped_record)
        
        # 数据库操作
        db_items = []
        for record in records:
            # 转换NaN为None
            cleaned_record = {k: (v if pd.notnull(v) else None) for k, v in record.items()}
            db_item = Materials(**cleaned_record)
            db.add(db_item)
            db_items.append(db_item)
        
        db.commit()
        logger.success(f"成功写入{len(records)}条数据")
        
        # 处理数据中的特殊值，确保JSON兼容
        preview_data = []
        for record in records[:10]:
            cleaned_record = {}
            for k, v in record.items():
                # 处理无穷大、NaN等特殊浮点数
                if isinstance(v, float):
                    if pd.isna(v) or np.isinf(v):  # 使用numpy的isinf替代pandas的isinf
                        cleaned_record[k] = None
                    else:
                        cleaned_record[k] = v
                else:
                    cleaned_record[k] = v
            preview_data.append(cleaned_record)
            
        return JSONResponse({
            "message": f"成功导入{len(records)}条数据",
            "columns": list(mapping.keys()),
            "data": preview_data  # 返回清理后的预览数据
        })
    except Exception as e:
        logger.error("文件处理失败", exc_info=e)
        
        error_detail = {
            "error": str(e),
            "filename": file.filename,
        }
        
        import traceback
        error_detail["stack_trace"] = traceback.format_exc()
            
        raise HTTPException(
            status_code=500,
            detail=error_detail
        )
