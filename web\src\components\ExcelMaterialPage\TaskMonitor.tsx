import React, { useState, useEffect } from 'react';
import {
  Card,
  Progress,
  Tag,
  Space,
  Button,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  Timeline,
  Spin
} from 'antd';
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Text, Title } = Typography;

interface TaskMonitorProps {
  taskId: string;
  onComplete?: (taskId: string) => void;
  onError?: (taskId: string, error: string) => void;
}

interface TaskStatus {
  task_id: string;
  status: string;
  progress: number;
  total_batches: number;
  completed_batches: number;
  failed_batches: number;
  created_at?: string;
  expires_at?: string;
  summary?: {
    total_processed: number;
    success_count: number;
    error_count: number;
    price_updated_count: number;
  };
}

export default function TaskMonitor({ taskId, onComplete, onError }: TaskMonitorProps) {
  const [taskStatus, setTaskStatus] = useState<TaskStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取任务状态
  const fetchTaskStatus = async () => {
    try {
      const response = await axios.get(`/api/excel-material/task/${taskId}/status`);
      const status = response.data;
      setTaskStatus(status);
      setError(null);

      // 检查任务是否完成
      if (status.status === 'completed' && onComplete) {
        onComplete(taskId);
      } else if (status.status === 'failed' && onError) {
        onError(taskId, '任务处理失败');
      }
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || '获取任务状态失败';
      setError(errorMsg);
      if (onError) {
        onError(taskId, errorMsg);
      }
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取状态，并设置定时刷新
  useEffect(() => {
    fetchTaskStatus();
    
    // 只有在任务未完成时才设置定时刷新
    const shouldRefresh = !taskStatus || 
      ['submitted', 'processing', 'ready_for_merge'].includes(taskStatus.status);
    
    if (shouldRefresh) {
      const interval = setInterval(fetchTaskStatus, 3000); // 每3秒刷新一次
      return () => clearInterval(interval);
    }
  }, [taskId, taskStatus?.status]);

  // 合并任务结果
  const handleMerge = async () => {
    try {
      await axios.post(`/api/excel-material/task/${taskId}/merge`);
      fetchTaskStatus(); // 刷新状态
    } catch (err: any) {
      setError(err.response?.data?.detail || '合并失败');
    }
  };

  // 下载结果文件
  const handleDownload = async () => {
    try {
      const response = await axios.get(`/api/excel-material/task/${taskId}/download`, {
        responseType: 'blob',
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `材料处理结果_${taskId}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.response?.data?.detail || '下载失败');
    }
  };

  // 获取状态配置
  const getStatusConfig = (status: string) => {
    const configs: Record<string, { color: string; text: string; icon: React.ReactNode }> = {
      submitted: { color: 'blue', text: '已提交', icon: <ClockCircleOutlined /> },
      processing: { color: 'orange', text: '处理中', icon: <SyncOutlined spin /> },
      ready_for_merge: { color: 'cyan', text: '待合并', icon: <ExclamationCircleOutlined /> },
      completed: { color: 'green', text: '已完成', icon: <CheckCircleOutlined /> },
      partial_success: { color: 'yellow', text: '部分成功', icon: <ExclamationCircleOutlined /> },
      failed: { color: 'red', text: '失败', icon: <ExclamationCircleOutlined /> },
      merge_failed: { color: 'red', text: '合并失败', icon: <ExclamationCircleOutlined /> },
    };
    return configs[status] || { color: 'default', text: status, icon: null };
  };

  // 生成时间线数据
  const getTimelineItems = () => {
    if (!taskStatus) return [];

    const items = [
      {
        color: 'green',
        children: (
          <div>
            <Text strong>任务已提交</Text>
            <br />
            <Text type="secondary">
              {taskStatus.created_at ? new Date(taskStatus.created_at).toLocaleString() : ''}
            </Text>
          </div>
        ),
      },
    ];

    if (taskStatus.status !== 'submitted') {
      items.push({
        color: taskStatus.status === 'processing' ? 'blue' : 'green',
        children: (
          <div>
            <Text strong>开始处理</Text>
            <br />
            <Text type="secondary">
              分批处理中，共 {taskStatus.total_batches} 个批次
            </Text>
          </div>
        ),
      });
    }

    if (taskStatus.status === 'ready_for_merge') {
      items.push({
        color: 'orange',
        children: (
          <div>
            <Text strong>批次处理完成</Text>
            <br />
            <Text type="secondary">等待合并结果</Text>
          </div>
        ),
      });
    }

    if (taskStatus.status === 'completed') {
      items.push({
        color: 'green',
        children: (
          <div>
            <Text strong>处理完成</Text>
            <br />
            <Text type="secondary">结果文件已生成</Text>
          </div>
        ),
      });
    }

    if (taskStatus.status === 'failed') {
      items.push({
        color: 'red',
        children: (
          <div>
            <Text strong>处理失败</Text>
            <br />
            <Text type="secondary">请检查错误信息</Text>
          </div>
        ),
      });
    }

    return items;
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在获取任务状态...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="获取任务状态失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchTaskStatus}>
              重试
            </Button>
          }
        />
      </Card>
    );
  }

  if (!taskStatus) {
    return (
      <Card>
        <Alert
          message="任务不存在"
          description="无法找到指定的任务"
          type="warning"
          showIcon
        />
      </Card>
    );
  }

  const statusConfig = getStatusConfig(taskStatus.status);

  return (
    <Card
      title={
        <Space>
          <Text strong>任务监控</Text>
          <Tag color={statusConfig.color} icon={statusConfig.icon}>
            {statusConfig.text}
          </Tag>
        </Space>
      }
      extra={
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchTaskStatus}
          size="small"
        >
          刷新
        </Button>
      }
    >
      {/* 进度显示 */}
      <div style={{ marginBottom: 24 }}>
        <Progress
          percent={Math.round(taskStatus.progress || 0)}
          status={taskStatus.status === 'failed' ? 'exception' : 'active'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">
            批次进度: {taskStatus.completed_batches || 0} / {taskStatus.total_batches || 0}
            {taskStatus.failed_batches > 0 && (
              <Text type="danger"> (失败: {taskStatus.failed_batches})</Text>
            )}
          </Text>
        </div>
      </div>

      {/* 统计信息 */}
      {taskStatus.summary && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="总处理数"
              value={taskStatus.summary.total_processed}
              valueStyle={{ fontSize: 16 }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="成功数"
              value={taskStatus.summary.success_count}
              valueStyle={{ color: '#3f8600', fontSize: 16 }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="失败数"
              value={taskStatus.summary.error_count}
              valueStyle={{ color: '#cf1322', fontSize: 16 }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="价格更新"
              value={taskStatus.summary.price_updated_count}
              valueStyle={{ color: '#1677ff', fontSize: 16 }}
            />
          </Col>
        </Row>
      )}

      {/* 操作按钮 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          {taskStatus.status === 'ready_for_merge' && (
            <Button
              type="primary"
              icon={<SyncOutlined />}
              onClick={handleMerge}
            >
              合并结果
            </Button>
          )}
          
          {taskStatus.status === 'completed' && (
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
            >
              下载结果
            </Button>
          )}
        </Space>
      </div>

      {/* 处理时间线 */}
      <div>
        <Title level={5}>处理进度</Title>
        <Timeline items={getTimelineItems()} />
      </div>

      {/* 任务信息 */}
      <div style={{ marginTop: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Text type="secondary">任务ID: </Text>
            <Text code copyable={{ text: taskStatus.task_id }}>
              {taskStatus.task_id.substring(0, 16)}...
            </Text>
          </Col>
          <Col span={12}>
            <Text type="secondary">过期时间: </Text>
            <Text>
              {taskStatus.expires_at ? new Date(taskStatus.expires_at).toLocaleString() : '-'}
            </Text>
          </Col>
        </Row>
      </div>
    </Card>
  );
}
