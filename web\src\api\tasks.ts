import request from '../utils/request';

/**
 * 运行任务
 * @param taskType 任务类型
 * @param params 任务参数
 * @returns 任务响应
 */
export async function runTask(taskType: string, params?: any) {
  return await request.post('/tasks/run', {
    task_type: taskType,
    params
  });
}

/**
 * 获取任务状态
 * @param taskId 任务ID
 * @returns 任务状态
 */
export async function getTaskStatus(taskId: string) {
  return await request.get(`/tasks/status/${taskId}`);
}

/**
 * 获取活跃任务列表
 * @returns 活跃任务列表
 */
export async function getActiveTasks() {
  return await request.get('/tasks/active');
}

/**
 * 获取已完成任务列表
 * @param limit 限制数量
 * @returns 已完成任务列表
 */
export async function getCompletedTasks(limit: number = 10) {
  return await request.get(`/tasks/completed?limit=${limit}`);
}

/**
 * 取消任务
 * @param taskId 任务ID
 * @returns 取消结果
 */
export async function cancelTask(taskId: string) {
  return await request.post(`/tasks/cancel/${taskId}`);
}
