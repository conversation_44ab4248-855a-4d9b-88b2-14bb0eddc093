import React from 'react';
import {
  Card,
  Steps,
  Typography,
  Row,
  Col,
  Alert,
  Tag,
  Space,
  Button,
  Divider,
  Image,
  List
} from 'antd';
import {
  FileExcelOutlined,
  UploadOutlined,
  SyncOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

export default function DemoPage() {
  const steps = [
    {
      title: '准备Excel文件',
      description: '确保Excel文件包含必需的列：名称、数量等',
      icon: <FileExcelOutlined />,
    },
    {
      title: '上传文件',
      description: '选择Excel文件并配置处理参数',
      icon: <UploadOutlined />,
    },
    {
      title: '分批处理',
      description: '系统自动分批处理数据，查询价格信息',
      icon: <SyncOutlined />,
    },
    {
      title: '合并结果',
      description: '等待所有批次完成后合并处理结果',
      icon: <CheckCircleOutlined />,
    },
    {
      title: '下载结果',
      description: '下载包含最终价格的Excel结果文件',
      icon: <DownloadOutlined />,
    },
  ];

  const features = [
    {
      title: '智能价格查询',
      description: '自动调用API查询最新价格信息',
      color: 'blue',
    },
    {
      title: '数据库匹配',
      description: '从现有材料库中查找匹配项',
      color: 'green',
    },
    {
      title: '分批异步处理',
      description: '大文件自动分批，避免超时',
      color: 'orange',
    },
    {
      title: '实时状态监控',
      description: '查看处理进度和详细状态',
      color: 'purple',
    },
    {
      title: '30天数据保留',
      description: '自动管理文件生命周期',
      color: 'cyan',
    },
    {
      title: '完整结果报告',
      description: '生成详细的处理统计信息',
      color: 'red',
    },
  ];

  const excelColumns = [
    { name: '名称', required: true, description: '产品名称，必需字段' },
    { name: '型号', required: false, description: '产品型号或规格' },
    { name: '规格', required: false, description: '详细规格说明' },
    { name: '数量', required: true, description: '产品数量，必需字段' },
    { name: '价格', required: false, description: '原始价格，可为空' },
    { name: '备注', required: false, description: '备注信息' },
    { name: '品牌', required: false, description: '产品品牌' },
    { name: '产地', required: false, description: '产品产地' },
    { name: '单位', required: false, description: '计量单位' },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <FileExcelOutlined /> Excel材料处理系统演示
      </Title>
      
      <Paragraph>
        本系统提供完整的Excel材料数据处理功能，支持分批异步处理、价格查询和数据库匹配。
      </Paragraph>

      {/* 处理流程 */}
      <Card title="处理流程" style={{ marginBottom: 24 }}>
        <Steps
          direction="horizontal"
          current={-1}
          items={steps}
          style={{ marginBottom: 24 }}
        />
        
        <Alert
          message="处理说明"
          description="系统会自动将Excel数据分批处理，每批默认50行。对每行数据进行API价格查询和数据库匹配，最终生成包含最优价格的结果文件。"
          type="info"
          showIcon
        />
      </Card>

      {/* 功能特性 */}
      <Card title="功能特性" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} md={8} key={index}>
              <Card size="small" style={{ height: '100%' }}>
                <Space direction="vertical" size="small">
                  <Tag color={feature.color}>{feature.title}</Tag>
                  <Text type="secondary">{feature.description}</Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* Excel格式要求 */}
      <Card title="Excel格式要求" style={{ marginBottom: 24 }}>
        <Alert
          message="重要提示"
          description="请确保Excel文件的第一行为标题行，包含以下列名。必需字段不能为空。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <List
          dataSource={excelColumns}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                title={
                  <Space>
                    <Text strong>{item.name}</Text>
                    {item.required ? (
                      <Tag color="red">必需</Tag>
                    ) : (
                      <Tag color="default">可选</Tag>
                    )}
                  </Space>
                }
                description={item.description}
              />
            </List.Item>
          )}
        />
      </Card>

      {/* 示例数据 */}
      <Card title="Excel示例数据" style={{ marginBottom: 24 }}>
        <div style={{ overflow: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ background: '#fafafa' }}>
                <th style={{ border: '1px solid #d9d9d9', padding: 8 }}>名称</th>
                <th style={{ border: '1px solid #d9d9d9', padding: 8 }}>型号</th>
                <th style={{ border: '1px solid #d9d9d9', padding: 8 }}>规格</th>
                <th style={{ border: '1px solid #d9d9d9', padding: 8 }}>数量</th>
                <th style={{ border: '1px solid #d9d9d9', padding: 8 }}>价格</th>
                <th style={{ border: '1px solid #d9d9d9', padding: 8 }}>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>电缆</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>YJV-3*120</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>3芯120平方</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>100</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>85.5</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>阻燃电缆</td>
              </tr>
              <tr>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>开关</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>DZ47-63</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>63A单极</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>50</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>12.8</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>小型断路器</td>
              </tr>
              <tr>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>插座</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>86型</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>五孔插座</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>200</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>8.5</td>
                <td style={{ border: '1px solid #d9d9d9', padding: 8 }}>墙壁插座</td>
              </tr>
            </tbody>
          </table>
        </div>
      </Card>

      {/* 价格优先级 */}
      <Card title="价格计算优先级" style={{ marginBottom: 24 }}>
        <Steps
          direction="vertical"
          size="small"
          current={-1}
          items={[
            {
              title: 'API价格',
              description: '最高优先级，来自外部价格查询API的最新价格信息',
              icon: <Text style={{ color: '#52c41a' }}>1</Text>,
            },
            {
              title: '数据库价格',
              description: '中等优先级，从现有材料库中匹配的价格信息',
              icon: <Text style={{ color: '#1677ff' }}>2</Text>,
            },
            {
              title: '原始价格',
              description: '最低优先级，Excel文件中的原始价格数据',
              icon: <Text style={{ color: '#8c8c8c' }}>3</Text>,
            },
          ]}
        />
      </Card>

      {/* 状态说明 */}
      <Card title="任务状态说明">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Space direction="vertical" size="small">
              <Space>
                <Tag color="blue" icon={<ClockCircleOutlined />}>已提交</Tag>
                <Text>任务已成功提交到处理队列</Text>
              </Space>
              <Space>
                <Tag color="orange" icon={<SyncOutlined spin />}>处理中</Tag>
                <Text>正在分批处理数据</Text>
              </Space>
              <Space>
                <Tag color="cyan" icon={<CheckCircleOutlined />}>待合并</Tag>
                <Text>所有批次完成，等待合并结果</Text>
              </Space>
            </Space>
          </Col>
          <Col xs={24} sm={12}>
            <Space direction="vertical" size="small">
              <Space>
                <Tag color="green" icon={<CheckCircleOutlined />}>已完成</Tag>
                <Text>处理完成，可下载结果文件</Text>
              </Space>
              <Space>
                <Tag color="yellow">部分成功</Tag>
                <Text>部分批次失败，但有可用结果</Text>
              </Space>
              <Space>
                <Tag color="red">失败</Tag>
                <Text>处理失败，请检查错误信息</Text>
              </Space>
            </Space>
          </Col>
        </Row>
      </Card>
    </div>
  );
}
