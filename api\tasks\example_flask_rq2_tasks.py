"""
Flask-RQ2 任务示例
展示如何使用Flask-RQ2装饰器定义任务
"""

import time
from ..core.rq_app import job, get_flask_rq
from ..core.debug import get_app_logger

# 获取日志记录器
logger = get_app_logger("flask_rq2_tasks")

@job(queue='default', timeout=300, result_ttl=3600, description="示例任务")
def example_task(name: str, delay: int = 5):
    """
    示例任务 - 使用Flask-RQ2装饰器
    
    Args:
        name: 任务名称
        delay: 延迟时间（秒）
    
    Returns:
        任务结果
    """
    logger.info(f"开始执行示例任务: {name}")
    
    # 模拟耗时操作
    for i in range(delay):
        time.sleep(1)
        logger.info(f"任务 {name} 进度: {i+1}/{delay}")
    
    result = f"任务 {name} 完成，耗时 {delay} 秒"
    logger.info(result)
    return result

@job(queue='high', timeout=600, result_ttl=7200, description="高优先级任务")
def high_priority_task(data: dict):
    """
    高优先级任务示例
    
    Args:
        data: 任务数据
    
    Returns:
        处理结果
    """
    logger.info(f"开始执行高优先级任务，数据: {data}")
    
    # 模拟数据处理
    processed_data = {
        "original": data,
        "processed_at": time.time(),
        "status": "completed"
    }
    
    logger.info("高优先级任务完成")
    return processed_data

@job(queue='low', timeout=1800, result_ttl=86400, description="低优先级批处理任务")
def batch_processing_task(items: list):
    """
    批处理任务示例
    
    Args:
        items: 要处理的项目列表
    
    Returns:
        处理结果统计
    """
    logger.info(f"开始批处理任务，项目数量: {len(items)}")
    
    results = {
        "total": len(items),
        "processed": 0,
        "failed": 0,
        "results": []
    }
    
    for i, item in enumerate(items):
        try:
            # 模拟处理每个项目
            time.sleep(0.1)  # 模拟处理时间
            processed_item = f"processed_{item}"
            results["results"].append(processed_item)
            results["processed"] += 1
            
            if i % 10 == 0:  # 每处理10个项目记录一次进度
                logger.info(f"批处理进度: {i+1}/{len(items)}")
                
        except Exception as e:
            logger.error(f"处理项目 {item} 时出错: {e}")
            results["failed"] += 1
    
    logger.info(f"批处理任务完成，成功: {results['processed']}, 失败: {results['failed']}")
    return results

# 使用函数方式提交任务的示例
def submit_example_tasks():
    """
    提交示例任务的函数
    """
    rq = get_flask_rq()
    
    # 提交示例任务
    job1 = example_task.queue("测试任务1", 3)
    logger.info(f"提交示例任务1，任务ID: {job1.id}")
    
    # 提交高优先级任务
    job2 = high_priority_task.queue({"type": "urgent", "value": 100})
    logger.info(f"提交高优先级任务，任务ID: {job2.id}")
    
    # 提交批处理任务
    items = [f"item_{i}" for i in range(50)]
    job3 = batch_processing_task.queue(items)
    logger.info(f"提交批处理任务，任务ID: {job3.id}")
    
    return [job1.id, job2.id, job3.id]

# 任务状态查询示例
def check_task_status(job_id: str):
    """
    检查任务状态
    
    Args:
        job_id: 任务ID
    
    Returns:
        任务状态信息
    """
    from ..core.rq_app import get_job
    
    try:
        job = get_job(job_id)
        if job is None:
            return {"status": "not_found", "message": "任务不存在"}
        
        return {
            "id": job.id,
            "status": job.get_status(),
            "result": job.result if job.is_finished else None,
            "created_at": job.created_at.isoformat() if job.created_at else None,
            "started_at": job.started_at.isoformat() if job.started_at else None,
            "ended_at": job.ended_at.isoformat() if job.ended_at else None,
            "exc_info": job.exc_info if job.is_failed else None,
            "meta": job.meta
        }
    except Exception as e:
        logger.error(f"查询任务状态时出错: {e}")
        return {"status": "error", "message": str(e)}
