"""
Flask-RQ2 任务示例
展示如何使用任务管理器定义和管理任务
"""

import time
from ..core.task_manager import job, submit_task, get_task_status, task_manager
from ..core.debug import get_app_logger

# 获取日志记录器
logger = get_app_logger("flask_rq2_tasks")

@job(queue='default', timeout=300, result_ttl=3600, description="示例任务")
def example_task(name: str, delay: int = 5):
    """
    示例任务 - 使用Flask-RQ2装饰器

    Args:
        name: 任务名称
        delay: 延迟时间（秒）

    Returns:
        任务结果
    """
    logger.info(f"开始执行示例任务: {name}")

    # 模拟耗时操作
    for i in range(delay):
        time.sleep(1)
        logger.info(f"任务 {name} 进度: {i+1}/{delay}")

    result = f"任务 {name} 完成，耗时 {delay} 秒"
    logger.info(result)
    return result

@job(queue='high', timeout=600, result_ttl=7200, description="高优先级任务")
def high_priority_task(data: dict):
    """
    高优先级任务示例

    Args:
        data: 任务数据

    Returns:
        处理结果
    """
    logger.info(f"开始执行高优先级任务，数据: {data}")

    # 模拟数据处理
    processed_data = {
        "original": data,
        "processed_at": time.time(),
        "status": "completed"
    }

    logger.info("高优先级任务完成")
    return processed_data

@job(queue='low', timeout=1800, result_ttl=86400, description="低优先级批处理任务")
def batch_processing_task(items: list):
    """
    批处理任务示例

    Args:
        items: 要处理的项目列表

    Returns:
        处理结果统计
    """
    logger.info(f"开始批处理任务，项目数量: {len(items)}")

    results = {
        "total": len(items),
        "processed": 0,
        "failed": 0,
        "results": []
    }

    for i, item in enumerate(items):
        try:
            # 模拟处理每个项目
            time.sleep(0.1)  # 模拟处理时间
            processed_item = f"processed_{item}"
            results["results"].append(processed_item)
            results["processed"] += 1

            if i % 10 == 0:  # 每处理10个项目记录一次进度
                logger.info(f"批处理进度: {i+1}/{len(items)}")

        except Exception as e:
            logger.error(f"处理项目 {item} 时出错: {e}")
            results["failed"] += 1

    logger.info(f"批处理任务完成，成功: {results['processed']}, 失败: {results['failed']}")
    return results

# 使用任务管理器提交任务的示例
def submit_example_tasks():
    """
    提交示例任务的函数 - 使用任务管理器
    """
    task_ids = []

    # 方式1：使用装饰器提交任务
    job1 = example_task.queue("测试任务1", 3)
    task_ids.append(job1.id)
    logger.info(f"提交示例任务1，任务ID: {job1.id}")

    # 方式2：使用任务管理器直接提交
    job2_id = submit_task(
        high_priority_task_func,
        {"type": "urgent", "value": 100},
        queue="high",
        timeout=600,
        description="高优先级任务示例"
    )
    task_ids.append(job2_id)
    logger.info(f"提交高优先级任务，任务ID: {job2_id}")

    # 方式3：使用装饰器提交批处理任务
    items = [f"item_{i}" for i in range(50)]
    job3 = batch_processing_task.queue(items)
    task_ids.append(job3.id)
    logger.info(f"提交批处理任务，任务ID: {job3.id}")

    return task_ids

# 不使用装饰器的任务函数
def high_priority_task_func(data: dict):
    """
    高优先级任务函数（不使用装饰器）
    """
    logger.info(f"开始执行高优先级任务，数据: {data}")

    # 模拟数据处理
    processed_data = {
        "original": data,
        "processed_at": time.time(),
        "status": "completed"
    }

    logger.info("高优先级任务完成")
    return processed_data

# 任务状态查询示例
def check_task_status(job_id: str):
    """
    检查任务状态 - 使用任务管理器

    Args:
        job_id: 任务ID

    Returns:
        任务状态信息
    """
    return get_task_status(job_id)

# 任务管理示例函数
def manage_tasks_example():
    """
    任务管理示例 - 展示各种任务操作
    """
    logger.info("开始任务管理示例")

    # 1. 提交任务
    task_id = submit_task(
        example_task_func,
        "管理示例任务",
        5,
        queue="default",
        description="任务管理示例"
    )
    logger.info(f"提交任务: {task_id}")

    # 2. 查询任务状态
    status = get_task_status(task_id)
    logger.info(f"任务状态: {status}")

    # 3. 获取队列信息
    queue_info = task_manager.get_queue_info()
    logger.info(f"队列信息: {queue_info}")

    # 4. 获取特定状态的任务
    queued_tasks = task_manager.get_tasks_by_status("queued")
    logger.info(f"排队中的任务数量: {len(queued_tasks)}")

    return {
        "task_id": task_id,
        "status": status,
        "queue_info": queue_info,
        "queued_tasks_count": len(queued_tasks)
    }

def example_task_func(name: str, delay: int = 5):
    """
    示例任务函数（不使用装饰器）
    """
    logger.info(f"开始执行示例任务: {name}")

    # 模拟耗时操作
    for i in range(delay):
        time.sleep(1)
        logger.info(f"任务 {name} 进度: {i+1}/{delay}")

    result = f"任务 {name} 完成，耗时 {delay} 秒"
    logger.info(result)
    return result
