"""
RQ应用配置模块
用于配置和初始化RQ队列
"""

import redis
from rq import Queue
from rq.job import Job
from rq_scheduler import Scheduler
from typing import Optional

from ..config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB, REDIS_URL

# 创建Redis连接
def get_redis_connection():
    """获取Redis连接"""
    return redis.Redis(
        host=REDIS_HOST,
        port=int(REDIS_PORT),
        password=REDIS_PASSWORD,
        db=int(REDIS_DB),
        decode_responses=True
    )

# 创建队列
default_queue = Queue("default", connection=get_redis_connection())
high_queue = Queue("high", connection=get_redis_connection())
low_queue = Queue("low", connection=get_redis_connection())

# 创建调度器
scheduler = Scheduler(connection=get_redis_connection(), queue=default_queue)

def get_rq_instance():
    """获取RQ实例（兼容旧代码）"""
    # 返回一个简单的对象，模拟RQ实例
    class RQInstance:
        def __init__(self):
            pass

        def queue(self, func, *args, **kwargs):
            """将任务加入队列"""
            queue_name = kwargs.pop("queue", "default")
            queue = get_queue(queue_name)
            return queue.enqueue(func, *args, **kwargs)

    return RQInstance()

def get_queue(queue_name: str = "default") -> Queue:
    """
    获取指定名称的队列

    Args:
        queue_name: 队列名称

    Returns:
        队列实例
    """
    if queue_name == "high":
        return high_queue
    elif queue_name == "low":
        return low_queue
    else:
        return default_queue

def get_scheduler() -> Scheduler:
    """
    获取调度器实例

    Returns:
        调度器实例
    """
    return scheduler

def get_job(job_id: str) -> Optional[Job]:
    """
    获取任务实例

    Args:
        job_id: 任务ID

    Returns:
        任务实例
    """
    return Job.fetch(job_id, connection=get_redis_connection())

def enqueue_job(func, *args, **kwargs) -> Job:
    """
    将任务加入队列

    Args:
        func: 任务函数
        *args: 位置参数
        **kwargs: 关键字参数

    Returns:
        任务实例
    """
    queue_name = kwargs.pop("queue", "default")
    queue = get_queue(queue_name)
    return queue.enqueue(func, *args, **kwargs)

def schedule_job(func, *args, **kwargs) -> Job:
    """
    调度定时任务

    Args:
        func: 任务函数
        *args: 位置参数
        **kwargs: 关键字参数

    Returns:
        任务实例
    """
    # 从kwargs中提取调度参数
    interval = kwargs.pop("interval", None)
    cron_string = kwargs.pop("cron_string", None)
    queue_name = kwargs.pop("queue", "default")

    # 获取调度器和队列
    scheduler = get_scheduler()
    queue = get_queue(queue_name)

    if interval is not None:
        # 按间隔调度
        return scheduler.schedule(
            scheduled_time=interval,
            func=func,
            args=args,
            kwargs=kwargs,
            queue_name=queue_name,
            repeat=kwargs.pop("repeat", None)
        )
    elif cron_string is not None:
        # 按Cron表达式调度
        return scheduler.cron(
            cron_string=cron_string,
            func=func,
            args=args,
            kwargs=kwargs,
            queue_name=queue_name
        )
    else:
        # 如果没有指定调度参数，则立即执行
        return queue.enqueue(func, *args, **kwargs)
