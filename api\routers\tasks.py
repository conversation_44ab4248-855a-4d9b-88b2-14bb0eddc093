"""
任务路由模块
提供与后台任务相关的API端点
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

from ..core.debug import get_app_logger
from ..tasks.sync_tasks import sync_materials, process_material_data
from ..core.rq_app import get_job, get_queue
from ..core.task_monitor import get_task_monitor

# 获取日志记录器
logger = get_app_logger("tasks_router")

# 创建路由
router = APIRouter(
    prefix="/tasks",
    tags=["tasks"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class TaskRequest(BaseModel):
    """任务请求模型"""
    task_type: str
    params: Optional[Dict[str, Any]] = None

# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    message: str

@router.post("/run", response_model=TaskResponse)
async def run_task(request: TaskRequest):
    """
    运行后台任务

    Args:
        request: 任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到任务请求: {request.task_type}")

    try:
        # 获取任务监控实例
        task_monitor = get_task_monitor()

        # 获取默认队列
        queue = get_queue()

        # 根据任务类型执行不同的任务
        if request.task_type == "sync_materials":
            # 执行材料同步任务
            job = queue.enqueue(sync_materials)
            task_id = job.id
            task_name = "sync_materials"

            # 注册任务到监控系统
            task_monitor.register_task(task_id, task_name)

            return TaskResponse(
                task_id=task_id,
                status="pending",
                message="材料同步任务已提交"
            )

        elif request.task_type == "process_material":
            # 检查必要参数
            if not request.params or "material_id" not in request.params:
                raise HTTPException(status_code=400, detail="缺少必要参数: material_id")

            # 执行材料处理任务
            material_id = request.params["material_id"]
            job = queue.enqueue(process_material_data, material_id)
            task_id = job.id
            task_name = "process_material_data"

            # 注册任务到监控系统
            task_monitor.register_task(task_id, task_name)

            return TaskResponse(
                task_id=task_id,
                status="pending",
                message=f"材料处理任务已提交 (材料ID: {material_id})"
            )

        else:
            # 未知任务类型
            raise HTTPException(status_code=400, detail=f"未知任务类型: {request.task_type}")

    except Exception as e:
        logger.error(f"提交任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.get("/status/{task_id}", response_model=Dict[str, Any])
async def get_task_status(task_id: str):
    """
    获取任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    logger.info(f"查询任务状态: {task_id}")

    try:
        # 获取任务监控实例
        task_monitor = get_task_monitor()

        # 获取任务状态
        task_status = task_monitor.get_task_status(task_id)

        if task_status:
            return task_status

        # 如果任务监控中没有找到任务，则直接从RQ获取
        job = get_job(task_id)

        if job is None:
            # 任务不存在
            return {
                "task_id": task_id,
                "status": "UNKNOWN",
                "message": "任务不存在或已被删除"
            }

        # 获取任务状态
        status = job.get_status()

        # 将RQ状态映射到我们的状态
        status_mapping = {
            "queued": "PENDING",
            "started": "STARTED",
            "finished": "SUCCESS",
            "failed": "FAILURE",
            "deferred": "PENDING",
            "scheduled": "PENDING",
            "canceled": "REVOKED"
        }

        mapped_status = status_mapping.get(status, status.upper())

        # 返回任务状态
        result = {
            "task_id": task_id,
            "status": mapped_status,
            "task_name": job.func_name if hasattr(job, 'func_name') else "unknown",
        }

        # 如果任务已完成，添加结果
        if status == "finished":
            result["result"] = job.result
        elif status == "failed":
            result["error"] = str(job.exc_info)

        return result

    except Exception as e:
        logger.error(f"获取任务状态时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务状态时出错: {str(e)}")

@router.get("/active", response_model=List[Dict[str, Any]])
async def get_active_tasks():
    """
    获取活跃任务列表

    Returns:
        活跃任务列表
    """
    logger.info("获取活跃任务列表")

    try:
        # 获取任务监控实例
        task_monitor = get_task_monitor()

        # 获取活跃任务
        active_tasks = task_monitor.get_active_tasks()
        return active_tasks

    except Exception as e:
        logger.error(f"获取活跃任务列表时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取活跃任务列表时出错: {str(e)}")

@router.get("/completed", response_model=List[Dict[str, Any]])
async def get_completed_tasks(limit: int = 10):
    """
    获取已完成任务列表

    Args:
        limit: 限制返回的任务数量

    Returns:
        已完成任务列表
    """
    logger.info(f"获取已完成任务列表 (limit={limit})")

    try:
        # 获取任务监控实例
        task_monitor = get_task_monitor()

        # 获取已完成任务
        completed_tasks = task_monitor.get_completed_tasks(limit)
        return completed_tasks

    except Exception as e:
        logger.error(f"获取已完成任务列表时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取已完成任务列表时出错: {str(e)}")

@router.post("/cancel/{task_id}", response_model=Dict[str, Any])
async def cancel_task_endpoint(task_id: str):
    """
    取消任务

    Args:
        task_id: 任务ID

    Returns:
        取消结果
    """
    logger.info(f"取消任务: {task_id}")

    try:
        # 获取任务
        job = get_job(task_id)

        if job is None:
            # 任务不存在
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在或已被删除")

        # 取消任务
        job.cancel()

        # 更新任务状态
        task_monitor = get_task_monitor()
        task_monitor.update_task_status(task_id, "REVOKED")

        return {
            "status": "success",
            "message": f"任务 {task_id} 已取消"
        }

    except Exception as e:
        logger.error(f"取消任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消任务时出错: {str(e)}")
