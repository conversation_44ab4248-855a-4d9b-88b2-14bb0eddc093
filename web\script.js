document.getElementById('upload-button').addEventListener('click', async () => {
    const fileInput = document.getElementById('excel-file-input');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('请先选择Excel文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('http://localhost:8000/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) throw new Error(`HTTP错误! 状态码: ${response.status}`);
        
        const result = await response.json();
        document.getElementById('upload-status').innerHTML = `
            <div class="success">
                成功导入 ${result.message}<br>
                识别到列：${result.columns.join(', ')}
            </div>
        `;
    } catch (error) {
        document.getElementById('upload-status').innerHTML = `
            <div class="error">
                上传失败: ${error.message}
            </div>
        `;
    }
});